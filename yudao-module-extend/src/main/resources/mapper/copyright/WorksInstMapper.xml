<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.iocoder.yudao.module.extend.mapper.copyright.WorksInstMapper">

    <resultMap id="worksInstMap" type="cn.iocoder.yudao.module.extend.entity.copyright.WorksInst">
        <id property="worksId" column="WORKS_ID"/>
        <result property="worksName" column="WORKS_NAME"/>
        <result property="worksType" column="WORKS_TYPE"/>
        <result property="publishPlace" column="PUBLISH_PLACE"/>
        <result property="createPlace" column="CREATE_PLACE"/>
        <result property="acceptOrg" column="ACCEPT_ORG"/>
        <result property="blockchainHash" column="BLOCKCHAIN_HASH"/>
        <result property="statusCd" column="STATUS_CD"/>
        <result property="statusDate" column="STATUS_DATE"/>
        <result property="createStaff" column="CREATE_STAFF"/>
        <result property="createDate" column="CREATE_DATE"/>
        <result property="updateStaff" column="UPDATE_STAFF"/>
        <result property="updateDate" column="UPDATE_DATE"/>
        <result property="remark" column="REMARK"/>
        <result property="blockchainId" column="BLOCKCHAIN_ID"/>
    </resultMap>


    <select id="getWorksInstPage" resultType="cn.iocoder.yudao.module.extend.entity.copyright.WorksInst">
        SELECT
        wi.WORKS_ID,
        wi.ACCEPTANCE_NUMBER,
        wi.WORKS_NAME,
        wi.owners_name,
        wi.WORKS_TYPE,
        wi.INDITE_NATURE,
        wi.WORKS_BELONG_TYPE,
        wi.COPYRIGHT_OBTAIN_CHANNEL,
        wi.CREATE_DATE,
        wi.CREATE_STAFF,
        wi.STATUS_CD,
        wi.audit_opinion,
        wi.SC_AUDIT_OPINION,
        wi.cer_no,
        wi.sample_file_id
        FROM works_inst AS wi
        where
        <choose>
            <when test="queryParam.statusCd !='' and queryParam.statusCd != null">
                wi.STATUS_CD=#{queryParam.statusCd}
            </when>
            <otherwise>
                wi.STATUS_CD != 2900
            </otherwise>
        </choose>
        <if test="queryParam.worksName !='' and queryParam.worksName != null">
            and wi.WORKS_NAME like CONCAT('%',#{queryParam.worksName},'%')
        </if>
        <if test="queryParam.ownersName !='' and queryParam.ownersName != null">
            and wi.owners_name like CONCAT('%',#{queryParam.ownersName},'%')
        </if>
        <if test="queryParam.worksType !='' and queryParam.worksType != null">
            and wi.WORKS_TYPE=#{queryParam.worksType}
        </if>
        <if test="queryParam.deptId !='' and queryParam.deptId != null">
            and wi.accept_org=#{queryParam.deptId}
        </if>
        <if test="queryParam.userIds !='' and queryParam.userIds != null">
            and wi.CREATE_STAFF in
        <foreach collection="queryParam.userIds" item="userId" open="(" separator="," close=")" >
            #{userId}
        </foreach>

        </if>
        <if test="queryParam.createEndtimeStart !='' and queryParam.createEndtimeStart != null">
            and wi.CREATE_DATE &gt;= #{queryParam.createEndtimeStart}
        </if>
        <if test="queryParam.createEndtimeEnd !='' and queryParam.createEndtimeEnd != null">
            and wi.CREATE_DATE &lt;= #{queryParam.createEndtimeEnd}
        </if>
--         GROUP BY wi.WORKS_ID
        order by wi.CREATE_DATE DESC
    </select>
    <select id="getOldWorksInst" resultType="cn.iocoder.yudao.module.extend.entity.copyright.WorksInst">
        SELECT wi.WORKS_ID,
               GROUP_CONCAT(CASE
                                WHEN att.ATTACHMENT_OBJECT_TYPE_CODE = 'sampleFiless'
                                    THEN att.ATTACH_ID END SEPARATOR ',') AS sampleFileId,
               GROUP_CONCAT(CASE
                                WHEN att.ATTACHMENT_OBJECT_TYPE_CODE = 'authorityGuarantee'
                                    THEN att.ATTACH_ID END SEPARATOR ',') AS powerGuaranteeId,
               GROUP_CONCAT(CASE
                                WHEN att.ATTACHMENT_OBJECT_TYPE_CODE = 'otherMaterial'
                                    THEN att.ATTACH_ID END SEPARATOR ',') AS otherMaterialsId
        FROM `works_inst` AS wi
                 LEFT JOIN works_attachment_rel AS rl ON wi.WORKS_ID = rl.WORKS_ID
                 LEFT JOIN attachment_info AS att ON att.ATTACH_ID = rl.ATTACH_ID
        WHERE wi.WORKS_ID = #{worksId}
    </select>
    <select id="getStatusNum" resultType="cn.iocoder.yudao.module.extend.vo.copyright.StatisticalDataVO">
        SELECT STATUS_CD       AS status,
               COUNT(WORKS_ID) AS statusNum
        FROM `works_inst` AS wi
        GROUP BY STATUS_CD
    </select>
    <select id="getUserCount" resultType="cn.iocoder.yudao.module.extend.vo.copyright.StatisticalDataVO">
        SELECT 'usersTotal' AS status, COUNT(*) AS statusNum
        FROM `sys_user`
        WHERE del_flag = 0
          AND `status` = 0
    </select>
    <select id="getInsertForDay" resultType="cn.iocoder.yudao.module.extend.vo.copyright.ChartDataVO">
        SELECT
        DATE( CREATE_DATE ) AS 'date',
        COUNT(*) AS 'count'
        FROM
        works_inst
        WHERE
        DATE( CREATE_DATE ) in (
        <foreach collection="dateList" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="deptId !='' and deptId != null">
            and accept_org=#{deptId}
        </if>
        GROUP BY DATE(CREATE_DATE)
    </select>
    <select id="getDeptListById" resultType="cn.iocoder.yudao.module.extend.vo.copyright.DeptVO">
        select dept_id AS id, dept_name AS deptName
        from sys_dept
        where find_in_set(#{id}, ancestors)
    </select>
    <select id="getRegisterForDay" resultType="cn.iocoder.yudao.module.extend.vo.copyright.ChartDataVO">
        SELECT
        DATE( CREATE_DATE ) AS 'date',
        COUNT(*) AS 'count'
        FROM
        works_inst
        WHERE
        CER_NO is not null
        AND STATUS_CD=1000
        and DATE( CREATE_DATE ) in (
        <foreach collection="dateList" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="deptId !='' and deptId != null">
            and accept_org=#{deptId}
        </if>
        GROUP BY DATE(CREATE_DATE)
    </select>
    <select id="regionalReport" resultType="cn.iocoder.yudao.module.extend.vo.copyright.ChartDataVO">
        SELECT
        accept_org AS deptId,
        COUNT( WORKS_ID ) AS count
        FROM
        `works_inst`
        WHERE
        CER_NO is not null
        AND STATUS_CD=1000
        <if test="type=='day'">
            and to_days( CREATE_DATE ) = to_days(now())
        </if>
        <if test="type=='month'">
            and DATE_FORMAT( CREATE_DATE, '%Y%m' ) = DATE_FORMAT( CURDATE( ), '%Y%m' )
        </if>
        <if test="type=='year'">
            and YEAR ( CREATE_DATE )= YEAR (NOW())
        </if>
        and accept_org in (
        <foreach collection="deptIdList" item="item" separator=",">
            #{item}
        </foreach>
        )
        GROUP BY
        accept_org
    </select>
    <select id="typeReport" resultType="cn.iocoder.yudao.module.extend.vo.copyright.ChartDataVO">
        SELECT
        WORKS_TYPE AS worksType,
        COUNT( WORKS_ID ) AS count
        FROM
        `works_inst`
        WHERE
        CER_NO is not null
        AND STATUS_CD=1000
        <if test="type=='day'">
            and to_days( CREATE_DATE ) = to_days(now())
        </if>
        <if test="type=='month'">
            and DATE_FORMAT( CREATE_DATE, '%Y%m' ) = DATE_FORMAT( CURDATE( ), '%Y%m' )
        </if>
        <if test="type=='year'">
            and YEAR ( CREATE_DATE )= YEAR (NOW())
        </if>
        GROUP BY
        WORKS_TYPE
    </select>
    <select id="customReport" resultType="cn.iocoder.yudao.module.extend.vo.copyright.CustomReportVO">
        SELECT
        dept_name AS deptName,
        <if test="worksType !='' and worksType != null">
            dict_label as worksTypeName,
        </if>
        COUNT( WORKS_ID ) AS count,
        DATE_FORMAT( CREATE_DATE, #{format} ) AS date
        FROM
        `works_inst`
        LEFT JOIN sys_dept ON accept_org=dept_id
        LEFT JOIN sys_dict_data ON dict_type='worksInstWorksType' AND dict_value=WORKS_TYPE
        WHERE
        CER_NO is not null
        AND STATUS_CD=1000
        and DATE_FORMAT( CREATE_DATE, #{format} ) in (
        <foreach collection="dateList" item="item" separator=",">
            #{item}
        </foreach>
        )
        <if test="deptIds !='' and deptIds != null">
            and accept_org in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="worksType !='' and worksType != null">
            and works_type in (
            <foreach collection="worksType" item="item" separator=",">
                #{item}
            </foreach>
            )
        </if>
        GROUP BY accept_org,date
        <if test="worksType !='' and worksType != null">
            ,works_type
        </if>
        ORDER BY accept_org,date,WORKS_TYPE
    </select>


</mapper>
