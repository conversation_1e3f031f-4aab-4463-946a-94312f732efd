<?xml version="1.0" encoding="UTF-8"?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="cn.iocoder.yudao.module.extend.mapper.copyright.FileEvidenceMapper">



    <select id="getWorksInstPage" resultType="cn.iocoder.yudao.module.extend.entity.copyright.FileEvidence">
        SELECT
        wi.WORKS_ID,
        wi.ACCEPTANCE_NUMBER,
        wi.WORKS_NAME,
        wi.owners_name,
        wi.blockchain_hash,
        wi.audit_opinion,
        wi.CREATE_DATE,
        wi.CREATE_STAFF,
        wi.STATUS_CD
        FROM file_evidence AS wi
        where
        <choose>
            <when test="queryParam.statusCd !='' and queryParam.statusCd != null">
                wi.STATUS_CD=#{queryParam.statusCd}
            </when>
            <otherwise>
                wi.STATUS_CD != 2900
            </otherwise>
        </choose>
        <if test="queryParam.worksName !='' and queryParam.worksName != null">
            and wi.WORKS_NAME like CONCAT('%',#{queryParam.worksName},'%')
        </if>
        <if test="queryParam.ownersName !='' and queryParam.ownersName != null">
            and wi.owners_name like CONCAT('%',#{queryParam.ownersName},'%')
        </if>
        <if test="queryParam.deptId !='' and queryParam.deptId != null">
            and wi.accept_org=#{queryParam.deptId}
        </if>
        <if test="queryParam.userId !='' and queryParam.userId != null">
            and wi.CREATE_STAFF=#{queryParam.userId}
        </if>
        <if test="queryParam.createEndtimeStart !='' and queryParam.createEndtimeStart != null">
            and wi.CREATE_DATE &gt;= #{queryParam.createEndtimeStart}
        </if>
        <if test="queryParam.createEndtimeEnd !='' and queryParam.createEndtimeEnd != null">
            and wi.CREATE_DATE &lt;= #{queryParam.createEndtimeEnd}
        </if>
        GROUP BY wi.WORKS_ID
        order by wi.CREATE_DATE DESC
    </select>


</mapper>
