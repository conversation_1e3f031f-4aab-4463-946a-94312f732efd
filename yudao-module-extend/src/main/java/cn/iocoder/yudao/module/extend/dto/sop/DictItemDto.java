package cn.iocoder.yudao.module.extend.dto.sop;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：weiqian
 * @Date：2022/7/26 16:48
 */
@Data
@ApiModel(value = "字典项")
@TableName(value = "sys_dict_item")
public class DictItemDto extends Model<DictItemDto> {

    @ApiModelProperty(value = "类型(作品类型:worksInstWorksType,创作性质:worksInstCreateNature,权利拥有方式:rightOwnership,作品归属情况:worksOwnership,权利取得方式:rightGet)",required = true)
    @NotBlank(message = "类型不能为空")
    private String type;
}

