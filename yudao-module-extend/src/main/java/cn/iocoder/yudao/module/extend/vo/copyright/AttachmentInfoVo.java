package cn.iocoder.yudao.module.extend.vo.copyright;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName IdentifyVo
 */
@Data
@ApiModel
public class AttachmentInfoVo {

    @ApiModelProperty(
            hidden = true
    )
    private Long attachId;
    @ApiModelProperty("附件所属对象类型")
    private String attachmentObjectTypeCode;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("MIME类型")
    private String mimeType;
    @ApiModelProperty("附件大小")
    private String fileSize;
    @ApiModelProperty("附件描述")
    private String fileDesc;
    @ApiModelProperty("附件路径")
    private String filePath;
    @ApiModelProperty("附件访问地址")
    private String fileUrl;
    @ApiModelProperty("图片缩略图路径，类型为图片时有值")
    private String fileScalePath;
    @ApiModelProperty("1000：有效状态  1100：无效状态 1200：已处理")
    private String statusCd;
    @ApiModelProperty("文件哈希值")
    private String hashCode;


}
