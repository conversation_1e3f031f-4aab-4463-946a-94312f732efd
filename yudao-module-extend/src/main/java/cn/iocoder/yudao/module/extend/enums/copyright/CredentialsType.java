package cn.iocoder.yudao.module.extend.enums.copyright;

/**
 * 著作权人证件类型
 *
 * <AUTHOR>
 * @date 2020-10-14
 **/
public enum CredentialsType {

    ID_CARD("identityCard","身份证"),
    BUSINESS_LICENSE("busiLicense","营业执照");

    CredentialsType(String code, String name){
        this.code=code;
        this.name=name;
    }

    private String code;

    private String name;

    public String getCode(){
        return this.code;
    }

    public static String getName(String code){
        for (CredentialsType ownerType : CredentialsType.values()){
            if (ownerType.getCode().equals(code)){
                return ownerType.name;
            }
        }
        return null;
    }


}
