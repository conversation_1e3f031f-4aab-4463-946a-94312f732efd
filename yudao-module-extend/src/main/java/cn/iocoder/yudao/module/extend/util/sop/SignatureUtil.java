package cn.iocoder.yudao.module.extend.util.sop;


import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.StringWriter;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * <AUTHOR>
 * @date 2022/9/22 - 17:50
 */
public class SignatureUtil {

    public static final String SIGN_TYPE_RSA = "RSA";

    public static final String SIGN_SHA256RSA_ALGORITHMS = "SHA256WithRSA";

    /**
     * 私钥加签
     * @param content 内容
     * @param privateKey 私钥
     * @param charset 编码
     * @return sgin
     * @throws Exception 异常
     */
    public static String rsa256Sign(String content, String privateKey,
                                    String charset) throws Exception {
        try {
            PrivateKey priKey = getPrivateKeyFromPKCS8(SIGN_TYPE_RSA,
                    new ByteArrayInputStream(privateKey.getBytes()));
            java.security.Signature signature = java.security.Signature
                    .getInstance(SIGN_SHA256RSA_ALGORITHMS);
            signature.initSign(priKey);
            if (StringUtil.isEmpty(charset)) {
                signature.update(content.getBytes());
            } else {
                signature.update(content.getBytes(charset));
            }
            byte[] signed = signature.sign();
            return new String(Base64.encodeBase64(signed));
        } catch (Exception e) {
            throw new Exception("无效签名;RSAcontent = " + content + "; charset = " + charset, e);
        }

    }


    public static PrivateKey getPrivateKeyFromPKCS8(String algorithm,
                                                    InputStream ins) throws Exception {
        if (ins == null || StringUtil.isEmpty(algorithm)) {
            return null;
        }
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        byte[] encodedKey = StreamUtil.readText(ins, "UTF-8").getBytes();
        encodedKey = Base64.decodeBase64(encodedKey);
        return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(encodedKey));
    }

    /**
     * 公钥验签
     * @param content 内容
     * @param sign 私钥签名
     * @param publicKey 公钥
     * @param charset 编码
     * @return
     * @throws Exception
     */
    public static boolean rsa256CheckContent(String content, String sign, String publicKey,
                                             String charset) throws Exception {
        try {
            System.out.println("=== RSA验签详细过程 ===");
            System.out.println("1. 开始解析公钥...");

            PublicKey pubKey = getPublicKeyFromX509("RSA",
                    new ByteArrayInputStream(publicKey.getBytes()));
            System.out.println("2. 公钥解析成功: " + pubKey.getAlgorithm());

            java.security.Signature signature = java.security.Signature
                    .getInstance(SIGN_SHA256RSA_ALGORITHMS);
            signature.initVerify(pubKey);
            System.out.println("3. 签名验证器初始化成功");

            byte[] contentBytes;
            if (StringUtil.isEmpty(charset)) {
                contentBytes = content.getBytes();
            } else {
                contentBytes = content.getBytes(charset);
            }
            signature.update(contentBytes);
            System.out.println("4. 内容更新成功，内容字节长度: " + contentBytes.length);

            byte[] signBytes = Base64.decodeBase64(sign.getBytes());
            System.out.println("5. 签名解码成功，签名字节长度: " + signBytes.length);

            boolean result = signature.verify(signBytes);
            System.out.println("6. 验签结果: " + result);
            System.out.println("=== RSA验签过程结束 ===");

            return result;
        } catch (Exception e) {
            System.out.println("RSA验签异常: " + e.getMessage());
            e.printStackTrace();
            throw new Exception("无效签名;RSAcontent = " + (content.length() > 100 ? content.substring(0, 100) + "..." : content) + "; charset = " + charset, e);
        }
    }

    public static PublicKey getPublicKeyFromX509(String algorithm,
                                                 InputStream ins) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance(algorithm);
        StringWriter writer = new StringWriter();
        StreamUtil.io(new InputStreamReader(ins), writer);
        String publicKeyContent = writer.toString();

        System.out.println("=== 公钥解析过程 ===");
        System.out.println("原始公钥内容长度: " + publicKeyContent.length());
        System.out.println("原始公钥前100字符: " + (publicKeyContent.length() > 100 ? publicKeyContent.substring(0, 100) + "..." : publicKeyContent));

        // 清理公钥格式：去掉PEM格式的头尾标识和换行符
        String cleanedPublicKey = cleanPublicKey(publicKeyContent);
        System.out.println("清理后公钥长度: " + cleanedPublicKey.length());
        System.out.println("清理后公钥前100字符: " + (cleanedPublicKey.length() > 100 ? cleanedPublicKey.substring(0, 100) + "..." : cleanedPublicKey));

        // 针对配置文件中的纯Base64格式公钥，直接使用原始内容
        try {
            System.out.println("尝试直接解析纯Base64格式公钥...");
            byte[] encodedKey = Base64.decodeBase64(publicKeyContent.trim().getBytes());
            System.out.println("Base64解码后字节长度: " + encodedKey.length);

            PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
            System.out.println("纯Base64格式公钥解析成功: " + publicKey.getAlgorithm());
            System.out.println("=== 公钥解析完成 ===");

            return publicKey;
        } catch (Exception e) {
            System.out.println("纯Base64格式解析失败: " + e.getMessage());

            // 尝试方法2：使用清理后的公钥内容
            try {
                System.out.println("尝试使用清理后的公钥内容...");
                byte[] encodedKey = Base64.decodeBase64(cleanedPublicKey.getBytes());
                System.out.println("清理后Base64解码字节长度: " + encodedKey.length);

                PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
                System.out.println("清理后公钥解析成功: " + publicKey.getAlgorithm());
                return publicKey;
            } catch (Exception e2) {
                System.out.println("清理后公钥解析也失败: " + e2.getMessage());

                // 尝试方法3：添加PEM格式头尾后解析
                try {
                    System.out.println("尝试添加PEM格式头尾...");
                    String pemFormattedKey = "-----BEGIN PUBLIC KEY-----\n" +
                                           publicKeyContent.trim() +
                                           "\n-----END PUBLIC KEY-----";

                    // 使用Java标准的PEM解析方式
                    String base64Key = pemFormattedKey
                        .replace("-----BEGIN PUBLIC KEY-----", "")
                        .replace("-----END PUBLIC KEY-----", "")
                        .replaceAll("\\s+", "");

                    byte[] encodedKey = Base64.decodeBase64(base64Key.getBytes());
                    System.out.println("PEM格式Base64解码字节长度: " + encodedKey.length);

                    PublicKey publicKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
                    System.out.println("PEM格式公钥解析成功: " + publicKey.getAlgorithm());
                    return publicKey;
                } catch (Exception e3) {
                    System.out.println("PEM格式解析也失败: " + e3.getMessage());

                    // 输出详细的调试信息
                    System.out.println("=== 公钥解析失败详细信息 ===");
                    System.out.println("原始公钥长度: " + publicKeyContent.length());
                    System.out.println("清理后公钥长度: " + cleanedPublicKey.length());
                    System.out.println("原始公钥前100字符: " + (publicKeyContent.length() > 100 ? publicKeyContent.substring(0, 100) + "..." : publicKeyContent));
                    System.out.println("清理后公钥前100字符: " + (cleanedPublicKey.length() > 100 ? cleanedPublicKey.substring(0, 100) + "..." : cleanedPublicKey));

                    throw new Exception("所有公钥解析方法都失败了。原始异常: " + e.getMessage() +
                                      ", 清理后异常: " + e2.getMessage() +
                                      ", PEM格式异常: " + e3.getMessage(), e);
                }
            }
        }
    }

    /**
     * 清理公钥格式，去掉PEM格式的头尾标识和换行符
     * @param publicKeyContent 原始公钥内容
     * @return 清理后的公钥内容
     */
    private static String cleanPublicKey(String publicKeyContent) {
        if (publicKeyContent == null) {
            return null;
        }

        System.out.println("开始清理公钥格式...");
        String original = publicKeyContent;

        // 去掉所有换行符、回车符、空格、制表符
        String cleaned = publicKeyContent.replaceAll("[\\s\\r\\n\\t]+", "");
        System.out.println("去掉空白字符后长度: " + cleaned.length());

        // 去掉PEM格式的头部标识（各种可能的格式）
        cleaned = cleaned.replace("-----BEGINPUBLICKEY-----", "");
        cleaned = cleaned.replace("-----BEGINRSAPUBLICKEY-----", "");
        cleaned = cleaned.replace("-----BEGIN PUBLIC KEY-----", "");
        cleaned = cleaned.replace("-----BEGIN RSA PUBLIC KEY-----", "");
        System.out.println("去掉头部标识后长度: " + cleaned.length());

        // 去掉PEM格式的尾部标识（各种可能的格式）
        cleaned = cleaned.replace("-----ENDPUBLICKEY-----", "");
        cleaned = cleaned.replace("-----ENDRSAPUBLICKEY-----", "");
        cleaned = cleaned.replace("-----END PUBLIC KEY-----", "");
        cleaned = cleaned.replace("-----END RSA PUBLIC KEY-----", "");
        System.out.println("去掉尾部标识后长度: " + cleaned.length());

        // 检查是否是有效的Base64字符串
        if (!cleaned.matches("^[A-Za-z0-9+/]*={0,2}$")) {
            System.out.println("警告：清理后的公钥包含非Base64字符");
            System.out.println("清理后内容前50字符: " + (cleaned.length() > 50 ? cleaned.substring(0, 50) : cleaned));
        }

        System.out.println("公钥清理完成");
        return cleaned;
    }
}
