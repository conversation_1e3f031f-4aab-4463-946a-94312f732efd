package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import cn.iocoder.yudao.module.extend.entity.copyright.WorksAuthor;

import javax.validation.constraints.*;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 登记作品
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@TableName("WORKS_INST")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "作品表")
public class WorksInst extends Model<WorksInst> {
private static final long serialVersionUID = 1L;

    /**
     * 作品ID，主键
     */
    @TableId(type=IdType.AUTO)
    @ApiModelProperty(value="作品ID",hidden = true)
    private Long worksId;

    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称",required = true)
    @NotBlank(message = "作品名称不能为空")
    private String worksName;
    /**
     * 著作权人名称，多个以逗号隔开
     */
    @ApiModelProperty(value="著作权人名称，多个以逗号隔开",required = true)
    @NotBlank(message = "著作权人不能为空")
    private String ownersName;
    /**
     * 作品类型   worksInstWorksType
     */
    @ApiModelProperty(value="作品类型",required = true)
    @NotBlank(message = "作品类型不能为空")
    @Pattern(regexp = "^[ABCDEFGHIJKLMNOP]$", message = "作品类型填写错误")
    private String worksType;
    /**
     * 样本文件ID，多个以逗号隔开
     */
    @ApiModelProperty(value="样本文件ID，多个以逗号隔开",required = true)
    @NotBlank(message = "样本文件ID不能为空")
    private String sampleFileId;
    /**
     * 权力保证书ID
     */
    @ApiModelProperty(value="权力保证书ID",required = true)
    @NotBlank(message = "权力保证书ID不能为空")
    private String powerGuaranteeId;
    /**
     * 其他证明材料ID，多个以逗号隔开
     */
    @ApiModelProperty(value="其他证明材料ID，多个以逗号隔开")
    private String otherMaterialsId;

    @TableField(exist = false)
    @ApiModelProperty(value="著作权人id",required = true)
    @NotEmpty(message = "著作权人id不能为空")
    private List<Long> ownersIds;
    /**
     * 作品封面
     */
    @ApiModelProperty(value="作品封面",hidden = true)
    private String worksCover;
    /**
     * 创作性质   worksInstCreateNature
     */
    @ApiModelProperty(value="创作性质",required = true)
    @NotBlank(message = "创作性质不能为空")
    @Pattern(regexp = "^[ABCDEFG]$", message = "创作性质填写错误")
    private String inditeNature;
    /**
     * 权利拥有方式     rightOwnership
     */
    @ApiModelProperty(value="权利拥有方式",required = true)
    @NotBlank(message = "权利拥有方式不能为空")
    @Pattern(regexp = "^[0-9,]+$", message = "权利拥有方式填写错误")
    private String copyrightOwnRange;
    /**
     * 作品归属情况   worksOwnership
     */
    @ApiModelProperty(value="作品归属情况",required = true)
    @NotBlank(message = "作品归属情况不能为空")
    @Pattern(regexp = "^[ABCDE]$", message = "作品归属情况填写错误")
    private String worksBelongType;
    /**
     * 权利取得方式    rightGet
     */
    @ApiModelProperty(value="权利取得方式",required = true)
    @NotBlank(message = "权利取得方式不能为空")
    @Pattern(regexp = "^[ABCD]$", message = "权利取得方式填写错误")
    private String copyrightObtainChannel;

    @TableField(exist = false)
    @ApiModelProperty(value = "作者集合")
    private List<WorksAuthor> worksInstAuthors;
    /**
     * 是否区块链存证
     */
    @ApiModelProperty(value="是否区块链存证(0:是，1:否)",hidden = true)
    private String isDeposited;
    /**
     * 作品HASH值
     */
    @ApiModelProperty(value="作品HASH值",hidden = true)
    private String worksHash;
    /**
     * 时间戳ID
     */
    @ApiModelProperty(value="时间戳ID",hidden = true)
    private Long tsaId;
    /**
     * 天平链ID
     */
    @ApiModelProperty(value="天平链ID",hidden = true)
    private Long balanceId;
    /**
     * 作品上链id
     */
    @ApiModelProperty(value="作品上链id",hidden = true)
    private Long blockchainId;
    /**
     * 作品上链hash
     */
    @ApiModelProperty(value="作品上链hash",hidden = true)
    private String blockchainHash;
    /**
     * 作品上链时间
     */
    @ApiModelProperty(value="作品上链时间",hidden = true)
    private LocalDateTime blockchainTime;
    /**
     * 存证证书地址
     */
    @ApiModelProperty(value="存证证书地址",hidden = true)
    private String certificateUrl;
    /**
     * 登记证书地址
     */
    @ApiModelProperty(value="登记证书地址",hidden = true)
    private String RegistrationCertificateUrl;

    /**
     * 作品创作说明
     */
    @ApiModelProperty(value="作品创作说明",required = true)
    @NotNull(message = "作品创作说明不能为空")
    private String worksAbstract;
    /**
     * 创作完成时间
     */
    @ApiModelProperty(value="创作完成时间",required = true)
    @JsonFormat(pattern="yyyy-MM-dd")
    @NotNull(message = "创作完成时间不能为空")
    @Past(message = "创作完成时间必须是过去时间")
    private Date inditeDoneTime;
    /**
     * 创作完成地点
     */
    @ApiModelProperty(value="创作完成地点",required = true)
    @NotNull(message = "创作完成地点能为空")
    private String createPlace;
    /**
     * 首次发表日期
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value="首次发表日期")
    @Past(message = "首次发表日期必须是过去时间")
    private Date publishDate;
    /**
     * 发表地点
     */
    @ApiModelProperty(value="发表地点")
    private String publishPlace;
    /**
     * 审核意见
     */
    @ApiModelProperty(value="审核意见",hidden = true)
//    @NotBlank(message = "审核意见")
    private String auditOpinion;

    @ApiModelProperty(value="省版权局审核意见",hidden = true)
    private String scAuditOpinion;
    /**
     * 获证时间
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value="获证时间",hidden = true)
    private LocalDateTime obtainedTime;

    @ApiModelProperty(value="省版权局证书编号",hidden = true)
    private String cerNo;
    /**
     * 受理区域
     */
    @ApiModelProperty(value="受理区域")
    private String acceptOrg;

    @TableField(exist = false)
    @ApiModelProperty(value="受理人")
    private String acceptor;

    @TableField(exist = false)
    @ApiModelProperty(value="受理人号码")
    private String acceptorPhone;

    @TableField(exist = false)
    @ApiModelProperty(value="是否是草稿")
    private Boolean isDraft;



    /**
    /**
     * 作品状态
     */
    @ApiModelProperty(value="作品状态",hidden = true)
    private String statusCd;
    /**
     * 状态时间
     */
    @ApiModelProperty(value="状态时间",hidden = true)
    private LocalDateTime statusDate;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人",hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间",hidden = true)
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人",hidden = true)
    @TableField(fill = FieldFill.UPDATE)
    private String updateStaff;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间",hidden = true)
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateDate;
    /**
     * 备注
     */
    @ApiModelProperty(value="备注",hidden = true)
    private String remark;


    @TableField(exist = false)
    @ApiModelProperty(value="版权编号，excel批量导入时使用")
    private String copyrightNo;

    @TableField(exist = false)
    @ApiModelProperty(value="共享证明文件【是、否】，excel批量导入时使用")
    private String shareFiles;

    @TableField(exist = false)
    @ApiModelProperty(value="作者字符串，excel批量导入时使用")
    private String author;
    }
