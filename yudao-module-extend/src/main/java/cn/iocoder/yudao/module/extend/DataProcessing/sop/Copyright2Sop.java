package cn.iocoder.yudao.module.extend.DataProcessing.sop;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.obs.services.ObsClient;
import cn.iocoder.yudao.module.extend.entity.copyright.AttachmentInfo;
import cn.iocoder.yudao.module.extend.entity.copyright.BusinessBlockchainLinkDO;
import cn.iocoder.yudao.module.extend.entity.copyright.EvidenceOwner;
import cn.iocoder.yudao.module.extend.entity.copyright.WcUser;
import cn.iocoder.yudao.module.extend.mapper.copyright.AttachmentInfoMapper;
import cn.iocoder.yudao.module.extend.mapper.copyright.BusinessBlockchainLinkMapper;
import cn.iocoder.yudao.module.extend.util.copyright.ObsUtil;
import cn.iocoder.yudao.module.extend.constant.copyright.BlockchainBusinessTypeConstants;
import cn.iocoder.yudao.module.extend.constant.sop.CommonConstants;
import cn.iocoder.yudao.module.extend.controller.sop.BlockChainController;
import cn.iocoder.yudao.module.extend.controller.sop.EvidenceAndRegisterController;
import cn.iocoder.yudao.module.extend.dto.sop.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


@Component
@Slf4j
@RequiredArgsConstructor
public class Copyright2Sop {

    private final EvidenceAndRegisterController evidenceAndRegisterController;

    private final BlockChainController blockChainController;

    @Autowired(required = false)
    private AttachmentInfoMapper attachmentInfoMapper;

    @Autowired
    private BusinessBlockchainLinkMapper businessBlockchainLinkMapper;

    @Autowired
    private ObsUtil obsUtil;

    @Value("${extend.copyright.temp}")
    private String tempPath;

    @Value("${extend.obs.bucket-name}")
    public String BucketName;


    /***
     * 用户注册上链
     */
    public WcUser foreign(RegisterForeignDto foreignDto) {
        JSONObject result = evidenceAndRegisterController.foreign(foreignDto);
        //解析json
        JSONObject data = JSONUtil.parseObj(result);
        if (StrUtil.isBlank(data.getStr("address"))) {
            log.error(result.toString());
//            throw new BusinessException(result.toString());
        }
        WcUser wcUser = new WcUser();
        wcUser.setUserId(Long.valueOf(foreignDto.getPlatformUserId()));
        wcUser.setPrivateKey(data.getStr("privateKey"));
        wcUser.setPublicKey(data.getStr("publicKey"));
        wcUser.setAddress(data.getStr("address"));
        wcUser.setCreateDate(LocalDateTime.now());
        wcUser.insert();
        return wcUser;
    }

    /***
     * 用户注册上链 - 返回原始JSONObject响应
     * 用于需要获取完整响应数据的场景
     */
    public JSONObject foreignWithRawResponse(RegisterForeignDto foreignDto) {
        return evidenceAndRegisterController.foreign(foreignDto);
    }

    /***
     * 文件上传
     */
    public String fileUpload(String attId, String typeCode, String platformUserId) {
        if (attachmentInfoMapper == null) {
            throw new RuntimeException("AttachmentInfoMapper not available - extend module may not be properly configured");
        }
        AttachmentInfo info = attachmentInfoMapper.selectById(attId);
        String[] paths = info.getFilePath().split("/");
        File file = null;
        try {
            ObsClient client = obsUtil.obsClient();
            InputStream inputStream = client.getObject(BucketName, paths[1] + "/" + paths[2]).getObjectContent();
            FileUtil.mkdir(tempPath);
            file = FileUtil.writeFromStream(inputStream, tempPath + StrUtil.SLASH + paths[paths.length - 1]);
//            MultipartFile cMultiFile = new MockMultipartFile(info.getFileName(), file.getName(), null, Files.newInputStream(file.toPath()));
            AttachmentUploadDto dto = new AttachmentUploadDto();
            dto.setFile(file);
            dto.setAttachmentObjectTypeCode(typeCode);
            dto.setPlatformUserId(platformUserId);
            dto.setFileDesc(info.getFileDesc());
            JSONObject jsonObject = evidenceAndRegisterController.fileUpload(dto);
            return String.valueOf(jsonObject.get("attachId"));
        } catch (Exception ioException) {
            ioException.printStackTrace();
            log.error("文件上传接口异常");
            throw new RuntimeException(ioException);
        } finally {
            assert file != null;
            file.delete();
        }
    }

    /***
     * 上链（重载方法，用于向后兼容）
     * @return
     */
    public Map<String, String> issueNft(WCDACDto dac, Long worksId, String address) {
        return issueNft(dac, worksId, address, BlockchainBusinessTypeConstants.NFT_ISSUE);
    }

    /***
     * 上链
     * @return
     */
    public Map<String, String> issueNft(WCDACDto dac, Long worksId, String address, String businessType) {
        JSONObject s = blockChainController.issueNft(dac);
        log.info("上链返回结果为：{}", s);
        //解析json
        JSONObject jsonObject = JSONUtil.parseObj(s);
        //com.alibaba.fastjson.JSONObject data = (com.alibaba.fastjson.JSONObject) jsonObject.get("data");
        BusinessBlockchainLinkDO blockchainLink = new BusinessBlockchainLinkDO();
        blockchainLink.setBusinessId(worksId); // 业务资产ID设置为作品ID
        blockchainLink.setBusinessType(businessType); // 业务类型：根据前端业务填写
        blockchainLink.setAddress(address); // 用户钱包地址
        blockchainLink.setDacId(Integer.valueOf(jsonObject.getJSONArray("dacIdList").get(0).toString()));
        blockchainLink.setWorksId(worksId);
        blockchainLink.setAmount(1L);
        blockchainLink.setCategoryId(52L);
        blockchainLink.setTo(address);
        blockchainLink.setStatusCd(jsonObject.getStr("status"));
        blockchainLink.setGasUsed(jsonObject.getStr("gasUsed"));
        blockchainLink.setBlockNumber(jsonObject.getStr("blockNumber"));
        blockchainLink.setTransactionHash(jsonObject.getStr("transactionHash"));
        blockchainLink.setHash(jsonObject.getStr("hash"));
        blockchainLink.setCreateDate(LocalDateTime.now());
        blockchainLink.setStatusDate(LocalDateTime.now());
        log.info("上链数据为：{}", blockchainLink);

        // 使用Mapper进行数据库插入
        businessBlockchainLinkMapper.insert(blockchainLink);
        //返回数据
        Map<String, String> map = new HashMap<>();
        map.put("blockchainId", String.valueOf(blockchainLink.getDacId()));
        map.put("transactionHash", blockchainLink.getTransactionHash());
        return map;
    }


    /***
     * 新增著作权人
     * 著作权人应用场景类型（存证：evidence，登记：registration）
     */
    public JSONObject addOwner(EvidenceOwner owners, String userId) {
        OwnerDto dto = new OwnerDto();
        dto.setOwnerSceneType(CommonConstants.OWNER_DELETE_EVIDENCE);
        dto.setOwnerName(owners.getOwnerName());
        dto.setIdNo(owners.getIdNo());
        dto.setOwnerType(owners.getOwnerType());
        dto.setPapersType(owners.getPapersType());
        dto.setSignature(owners.getSignature());
        dto.setLegalName(owners.getLegalName());
        dto.setPlatformUserId(userId);
        return evidenceAndRegisterController.addOwner(dto);
    }

    /***
     * 新增存证
     * @return
     */
    public JSONObject fileEvidence(FileEvidenceDto fileEvidenceDto) {
        try {
            JSONObject s = evidenceAndRegisterController.save(fileEvidenceDto);
            log.info("上链返回结果为：{}", s);
            JSONObject jsonObject = JSONUtil.parseObj(s);
            return jsonObject;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /***
     * 存证-获取存证证书
     * @return
     */
    public String depositCertificate(FileEvidenceByWorkIdDto workIdDto) {
        JSONObject s = evidenceAndRegisterController.depositCertificate(workIdDto);
        log.info("上链返回结果为：{}", s);
        return s.getStr("certificateUrl");
    }
}
