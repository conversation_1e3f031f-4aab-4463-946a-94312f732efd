package cn.iocoder.yudao.module.extend.util.sop;



import cn.hutool.http.HttpException;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.databind.ObjectMapper;
import cn.iocoder.yudao.module.extend.config.sop.SopProperties;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
public class HttpUtil {

    private final SopProperties sopProperties;

    private static String charset = "utf-8";

    /**
     * 发送请求
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doPost(Object bean,  Boolean arrayFlag,String method,String version) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"POST");
            log.info(body.toString());
            String reqBody = HttpRequest.post(sopProperties.getAccessUrl())
                    .form(body)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }
    /**
     * 文件上传
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doPostUpload(Object bean,  Boolean arrayFlag,String method,String version,File file) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"POST");
            log.info(body.toString());
            String reqBody = HttpRequest.post(sopProperties.getAccessUrl())
                    .form(body)
                    .form("file",file)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }

    /**
     * 发送请求
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    public String doGet(Object bean, Boolean arrayFlag,String method,String version) {
        try {
            String response=getResponse(method);
            Map<String, Object> body = getRequestMap(bean, method, version,"GET");
            String reqBody = HttpRequest.get(sopProperties.getAccessUrl())
                    .form(body)
                    .timeout(10000)
                    .execute()
                    .body();
            // 验签
            signCheck(reqBody, response);
            // 结果json解析
            return resultJsonAnalysis(reqBody, response, arrayFlag);
        } catch (HttpException e) {
            log.error("开放平台请求异常:{}", e.getMessage());
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }

    private String getResponse(String method){
        String[] strings = method.split("\\.");
        StringBuilder response = new StringBuilder();
        for (int i = 0; i < strings.length; i++) {
            response.append(strings[i]).append(i == strings.length - 1 ? "_response" : "_");
        }
        return response.toString();
    }

    /**
     * 组装请求参数
     * @param bean 组装请求对象
     * @return
     */
    @SneakyThrows
    public Map<String, Object> getRequestMap(Object bean, String method, String version, String httpMethod) {
//        Map<String, Object> contentMap = BeanUtil.beanToMap(bean);
        ObjectMapper objectMapper=new ObjectMapper();
        String contentStr = objectMapper.writeValueAsString(bean);
        Map<String,Object>params=new HashMap<>();
        // 公共请求参数
        params.put("app_id", sopProperties.getAppKey());
        params.put("method", method);
        params.put("version", version);
        params.put("format", "json");
        params.put("charset", "utf-8");
        params.put("sign_type", "RSA2");
        params.put("httpMethod", httpMethod);
        params.put("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        params.put("biz_content",contentStr);
        String content = getSignContent(params);
        String sign = null;
        try {
            sign = SignatureUtil.rsa256Sign(content, sopProperties.getPrivateKey(), charset);
        } catch (Exception e) {
            throw new RuntimeException("构建签名失败", e);
        }
        params.put("sign", sign);
        return params;
    }

    /**
     * 业务字段字母升序排序
     * @param sortedParams 业务字段
     * @return
     */
    private String getSignContent(Map<String, Object> sortedParams) {
        StringBuffer content = new StringBuffer();
        List<String> keys = new ArrayList<String>(sortedParams.keySet());
        Collections.sort(keys);
        int index = 0;
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            String value = sortedParams.get(key).toString();
            if (StringUtil.areNotEmpty(key, value)) {
                content.append((index == 0 ? "" : "&") + key + "=" + value);
                index++;
            }
        }
        return content.toString();
    }

    /**
     * 平台公钥验签
     * @param reqBody 响应body
     * @param response 响应标识
     */
    private void signCheck(String reqBody, String response) {
        try {
            JSONObject result = JSONUtil.parseObj(reqBody);
            log.info("原始响应数据: {}", reqBody);

            String sign = result.get("sign").toString();
            log.info("提取的签名: {}", sign);

            // 改进：使用JSON解析方式提取验签内容，而不是字符串替换
            String signContent = extractSignContent(result, response);
            log.info("用于验签的内容: {}", signContent);

            // 添加详细的验签调试信息
            String publicKey = sopProperties.getPlatformPublicKey();
            log.info("验签参数详情:");
            log.info("- 签名内容长度: {}", signContent.length());
            log.info("- 签名内容前100字符: {}", signContent.length() > 100 ? signContent.substring(0, 100) + "..." : signContent);
            log.info("- 签名值长度: {}", sign.length());
            log.info("- 公钥长度: {}", publicKey != null ? publicKey.length() : "null");
            log.info("- 字符编码: {}", charset);

            boolean flag;
            try {
                flag = SignatureUtil.rsa256CheckContent(
                        signContent,
                        sign,
                        publicKey,
                        charset);
                log.info("RSA验签结果: {}", flag);
            } catch (Exception e) {
                log.error("RSA验签过程中发生异常: {}", e.getMessage(), e);
                throw new RuntimeException("验签过程异常: " + e.getMessage(), e);
            }

            if (!flag) {
                log.error("开放平台验签失败 - 详细信息:");
                log.error("- 签名内容: {}", signContent);
                log.error("- 签名值: {}", sign);
                log.error("- 公钥前50字符: {}", publicKey != null && publicKey.length() > 50 ? publicKey.substring(0, 50) + "..." : publicKey);

                // 尝试不同的验签内容格式
                log.warn("尝试其他验签内容格式...");
                tryAlternativeSignContent(signContent, sign, publicKey, charset);

                throw new RuntimeException("请求异常，请联系管理员");
            }

            log.info("验签成功");
        } catch (Exception e) {
            log.error("开放平台验签异常:{}", e.getMessage(), e);
            throw new RuntimeException("请求异常，请联系管理员");
        }
    }

    /**
     * 提取用于验签的内容
     * @param result 完整的响应JSON对象
     * @param response 响应标识
     * @return 用于验签的内容字符串
     */
    private String extractSignContent(JSONObject result, String response) {
        try {
            log.info("开始提取验签内容，response标识: {}", response);
            log.info("完整响应JSON对象: {}", result.toString());

            // 方法1：尝试提取业务数据部分（修复JSONObject获取方式）
            Object responseObj = result.get(response);
            log.info("提取到的响应对象类型: {}", responseObj != null ? responseObj.getClass().getName() : "null");

            if (responseObj != null) {
                JSONObject responseData;
                if (responseObj instanceof JSONObject) {
                    responseData = (JSONObject) responseObj;
                } else {
                    // 如果不是JSONObject，尝试转换
                    responseData = JSONUtil.parseObj(responseObj.toString());
                }

                log.info("成功提取到业务数据: {}", responseData.toString());
                String signContent = JSONUtil.toJsonStr(responseData);
                log.info("方法1 - 业务数据验签内容: {}", signContent);
                return signContent;
            }

            // 方法2：如果方法1失败，尝试去掉sign字段后的完整内容
            log.warn("方法1失败，尝试方法2：去掉sign字段的完整内容");
            JSONObject clonedResult = JSONUtil.parseObj(result.toString());
            clonedResult.remove("sign");
            String signContent = JSONUtil.toJsonStr(clonedResult);
            log.info("方法2 - 去掉sign字段的验签内容: {}", signContent);
            return signContent;

        } catch (Exception e) {
            log.error("提取验签内容时发生异常: {}", e.getMessage(), e);

            // 方法3：最后的备用方案，使用原来的字符串替换方法
            log.warn("尝试方法3：使用原来的字符串替换方法");
            try {
                String originalReqBody = JSONUtil.toJsonStr(result);
                String sign = result.get("sign").toString();
                String signContent = originalReqBody.replace("{\"" + response + "\":", "").replace(",\"sign\":\"" + sign + "\"}", "");
                log.info("方法3 - 字符串替换验签内容: {}", signContent);
                return signContent;
            } catch (Exception ex) {
                log.error("所有方法都失败了: {}", ex.getMessage(), ex);
                throw new RuntimeException("提取验签内容失败: " + ex.getMessage(), ex);
            }
        }
    }

    /**
     * 尝试不同的验签内容格式
     * @param originalSignContent 原始验签内容
     * @param sign 签名值
     * @param publicKey 公钥
     * @param charset 字符编码
     */
    private void tryAlternativeSignContent(String originalSignContent, String sign, String publicKey, String charset) {
        try {
            // 尝试1: 去掉所有空格和换行符
            String compactContent = originalSignContent.replaceAll("\\s+", "");
            log.info("尝试1 - 紧凑格式验签内容长度: {}", compactContent.length());
            boolean result1 = SignatureUtil.rsa256CheckContent(compactContent, sign, publicKey, charset);
            log.info("尝试1 - 紧凑格式验签结果: {}", result1);

            // 尝试2: 原始JSON字符串（不经过JSONUtil格式化）
            log.info("尝试2 - 使用原始JSON格式");
            // 这里需要从原始响应中重新提取，暂时跳过

            // 尝试3: 尝试不同的字符编码
            log.info("尝试3 - 使用不同字符编码");
            boolean result3 = SignatureUtil.rsa256CheckContent(originalSignContent, sign, publicKey, "UTF-8");
            log.info("尝试3 - UTF-8编码验签结果: {}", result3);

            // 尝试4: 检查签名值是否需要URL解码
            log.info("尝试4 - 检查签名值格式");
            log.info("签名值是否包含URL编码字符: {}", sign.contains("%") || sign.contains("+") || sign.contains("/"));

        } catch (Exception e) {
            log.error("尝试替代验签格式时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     *
     * 结果json解析
     * @param reqBody 返回结果
     * @param response 响应标识
     * @param arrayFlag 是否数组JSON标识
     * @return
     */
    private String resultJsonAnalysis(String reqBody, String response, Boolean arrayFlag) {
        try {
            log.info("开始解析结果JSON，response标识: {}", response);
            log.info("reqBody内容: {}", reqBody);

            JSONObject result = JSONUtil.parseObj(reqBody);
            log.info("JSON解析成功");

            // 修复：使用安全的方式获取JSONObject，避免Hutool版本兼容性问题
            Object responseObj = result.get(response);
            if (responseObj == null) {
                log.error("响应数据中缺少{}字段", response);
                throw new RuntimeException("请求异常，请联系管理员");
            }

            JSONObject responseJson;
            if (responseObj instanceof JSONObject) {
                responseJson = (JSONObject) responseObj;
            } else {
                // 如果不是JSONObject，尝试转换
                responseJson = JSONUtil.parseObj(responseObj.toString());
            }

            log.info("成功获取响应JSON对象，code: {}", responseJson.get("code"));

            if (!"10000".equals(responseJson.get("code"))) {
                log.error("开放平台请求异常:{}", reqBody);
                if (responseJson.containsKey("sub_msg")) {
                    throw new RuntimeException(responseJson.getStr("sub_msg"));
                } else if (responseJson.containsKey("msg")) {
                    throw new RuntimeException(responseJson.getStr("msg"));
                }
                throw new RuntimeException("请求异常，请联系管理员");
            }

            if (arrayFlag) {
                log.info("处理数组类型数据");
                Object dataObj = responseJson.get("data");
                if (dataObj instanceof cn.hutool.json.JSONArray) {
                    cn.hutool.json.JSONArray data = (cn.hutool.json.JSONArray) dataObj;
                    return JSON.toJSONString(data);
                } else {
                    cn.hutool.json.JSONArray data = JSONUtil.parseArray(dataObj.toString());
                    return JSON.toJSONString(data);
                }
            }

            log.info("处理对象类型数据");
            Object dataObj = responseJson.get("data");
            if (Objects.isNull(dataObj)) {
                log.info("data字段为空，返回完整响应");
                return JSON.toJSONString(responseJson);
            }

            JSONObject dataJson;
            if (dataObj instanceof JSONObject) {
                dataJson = (JSONObject) dataObj;
            } else {
                dataJson = JSONUtil.parseObj(dataObj.toString());
            }

            log.info("成功获取data字段");

            // 检查是否有嵌套的data字段
            Object dataTwoObj = dataJson.get("data");
            if (Objects.isNull(dataTwoObj)) {
                log.info("返回一级data数据");
                return JSON.toJSONString(dataJson);
            } else {
                log.info("检测到二级data字段，返回二级data数据");
                JSONObject dataTwoJson;
                if (dataTwoObj instanceof JSONObject) {
                    dataTwoJson = (JSONObject) dataTwoObj;
                } else {
                    dataTwoJson = JSONUtil.parseObj(dataTwoObj.toString());
                }
                return JSON.toJSONString(dataTwoJson);
            }

        } catch (Exception e) {
            log.error("结果JSON解析异常: {}", e.getMessage(), e);
            throw new RuntimeException("结果解析失败: " + e.getMessage(), e);
        }
    }


}
