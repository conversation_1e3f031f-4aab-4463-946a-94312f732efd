package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Data
@TableName("IDENTIFY")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "实名认证")
public class Identify extends Model<Identify> {
private static final long serialVersionUID = 1L;

    /**
     * 主键
     */

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="主键")
    private Long identifyId;
    /**
     * 住址
     */
    @ApiModelProperty(value="住址")
    private String address;

    /**
     * 用户ID
     */
    @ApiModelProperty(value="用户ID")
    private Long userId;
    /**
     * 姓名
     */
    @ApiModelProperty(value="姓名")
    private String legalName;
    /**
     * 身份证
     */
    @ApiModelProperty(value="身份证")
    private String idNo;
    /**
     * 手机
     */
    @ApiModelProperty(value="手机")
    private String phone;
    /**
     * 身份证照片
     */
    @ApiModelProperty(value="身份证照片")
    private String idCard;
    /**
     * 认证类型,0:个人 1企业
     */
    @ApiModelProperty(value="认证类型,0:个人 1企业")
    private String identifyType;
    /**
     * 营业执照照片
     */
    @ApiModelProperty(value="营业执照照片")
    private String license;
    /**
     * 营业执照编号
     */
    @ApiModelProperty(value="营业执照编号")
    private String licenseNo;

    /**
     * 法人姓名
     */
    @ApiModelProperty(value="法人姓名")
    private String operName;
    /**
     * 状态
     */
    @ApiModelProperty(value="状态")
    private String statusCd;
    /**
     * 状态时间
     */
    @ApiModelProperty(value="状态时间")
    private LocalDateTime statusDate;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    private String updateStaff;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateDate;
    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @ApiModelProperty(value="证件信息识别出来的信息")
    private String cardMessage;


    @ApiModelProperty(value="国籍")
    private String country;
    /**
     * 省
     */
    @ApiModelProperty(value="省")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty(value="市")
    private String city;

    @ApiModelProperty(value="证件类型")
    private String papersType;
    }
