package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "登记确权对外注册功能")
public class RegisterForeignDto {
    public RegisterForeignDto() {
    }

    @ApiModelProperty(value="用户名", required = true)
    private String name;

    @ApiModelProperty(value="证件号码（身份证号码/社会统一代码）", required = true)
    private String number;

    @ApiModelProperty(value="证件类型（身份证：identityCard，营业执照：busiLicense）", required = true)
    private String type;

    @ApiModelProperty(value="法人姓名（企业必传）")
    private String legalName;

    @ApiModelProperty(value = "平台用户主键(平台注册时传000)")
    private String platformUserId;

    public RegisterForeignDto(String name, String number, String type, String legalName, String platformUserId) {
        this.name = name;
        this.number = number;
        this.type = type;
        this.legalName = legalName;
        this.platformUserId = platformUserId;
    }
}