package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

@Data
@TableName("file_evidence")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "版权存证")
public class FileEvidence extends Model<FileEvidence> {
    private static final long serialVersionUID = 1L;
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="作品ID，主键")
    private Long worksId;
    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;
    @ApiModelProperty(value="作品名称")
    @NotBlank(message = "作品名称不能为空")
    private String worksName;
    @ApiModelProperty(value="作品HASH值")
    private String worksHash;
    @ApiModelProperty(value="上链 ID")
    private Long blockchainId;
    @ApiModelProperty(value="上链HASH")
    private String blockchainHash;
    @ApiModelProperty(value="上链时间")
    private LocalDateTime blockchainTime;
    @ApiModelProperty(value="著作权人ID")
    @NotBlank(message = "著作权人不能为空")
    private String ownersIds;
    @ApiModelProperty(value="著作权人名称")
    @NotBlank(message = "著作权人名称不能为空")
    private String ownersName;
    @ApiModelProperty(value="样本文件ID")
    @NotBlank(message = "样本文件不能为空")
    private String sampleFileId;
    @ApiModelProperty(value="时间戳ID")
    private Long tsaId;
    @ApiModelProperty(value="天平链ID")
    private Long balanceId;
    @ApiModelProperty(value="存证证书地址")
    private String certificateUrl;
    @ApiModelProperty(value="审核意见")
    private String auditOpinion;
    @ApiModelProperty(value="状态")
    private String statusCd;
    @ApiModelProperty(value="状态时间")
    private LocalDateTime statusDate;
    @ApiModelProperty(value="创建人")
    private String createStaff;
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
    @ApiModelProperty(value="修改人")
    private String updateStaff;
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateDate;
    @ApiModelProperty(value="备注")
    private String remark;
    @ApiModelProperty(value="受理区域")
    private String acceptOrg;



    @TableField(exist = false)
    @ApiModelProperty(value="受理人")
    private String acceptor;

    @TableField(exist = false)
    @ApiModelProperty(value="受理人号码")
    private String acceptorPhone;

    @TableField(exist = false)
    @ApiModelProperty(value="著作权人列表")
    private List<EvidenceOwner> ownerList;

    @TableField(exist = false)
    @ApiModelProperty(value="样本文件")
    private AttachmentInfo sampleFile;

}
