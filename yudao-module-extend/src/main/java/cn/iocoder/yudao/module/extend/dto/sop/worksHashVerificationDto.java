package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "作品hash核验")
public class worksHashVerificationDto {

    @ApiModelProperty(value = "文件")
    private MultipartFile file;

    @ApiModelProperty(value = "数据指纹")
    private String worksHash;

    @ApiModelProperty(value = "统一证据编号", required = true)
    private String blockChainHash;

}