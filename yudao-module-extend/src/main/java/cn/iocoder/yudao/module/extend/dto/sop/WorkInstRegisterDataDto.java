package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 *
 * <AUTHOR>
 * @date 2023/3/13
 **/
@Data
public class WorkInstRegisterDataDto {


    @ApiModelProperty(value="作品ID")
    private Long worksId;

    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;

    @NotBlank(message = "作品名称不能为空")
    private String worksName;

    @ApiModelProperty(value="作品HASH值")
    private String worksHash;

    @ApiModelProperty(value="文创链上链hash")
    private String blockchainHash;

    @ApiModelProperty(value="作品上链时间")
    private LocalDateTime blockchainTime;

    @ApiModelProperty(value="天平链存证hash值")
    private String bcHash;

    @ApiModelProperty(value="时间戳返回时间")
    private String ntscTime;

    @ApiModelProperty(value="时间戳返回信息")
    private byte[] ntscRes;

    @ApiModelProperty(value="存证证书地址")
    private String certificateUrl;

    @ApiModelProperty(value="获证时间")
    private LocalDateTime obtainedTime;

    @ApiModelProperty(value="省版权局证书编号")
    private String certNo;

    @ApiModelProperty(value="登记证书地址")
    private String RegistrationCertificateUrl;

}