package cn.iocoder.yudao.module.extend.config.sop;

import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.support.EncodedResource;
import org.springframework.core.io.support.PropertySourceFactory;

import javax.validation.constraints.NotNull;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Properties;

/**
 * @ClassName YamlPropertySourceFactory
 * <AUTHOR> 102306
 * @Date 2022/4/19 17:20
 */
public class YamlPropertySourceFactory implements PropertySourceFactory {

        @Override
        public @NotNull PropertySource< ? > createPropertySource(String name, @NotNull EncodedResource resource) throws IOException {
            Properties propertiesFromYaml = loadYamlIntoProperties(resource);
            String sourceName = name != null ? name : resource.getResource().getFilename();
            assert sourceName != null;
            return new PropertiesPropertySource(sourceName, propertiesFromYaml);
        }

        private Properties loadYamlIntoProperties(EncodedResource resource) throws FileNotFoundException {
            try {
                YamlPropertiesFactoryBean factory = new YamlPropertiesFactoryBean();
                factory.setResources(resource.getResource());
                factory.afterPropertiesSet();
                return factory.getObject();
            } catch (IllegalStateException e) {
                Throwable cause = e.getCause();
                if (cause instanceof FileNotFoundException) {
                    throw (FileNotFoundException) e.getCause();
                }
                throw e;
            }
        }

}
