package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "作品ID查询证书参数")
public class FileEvidenceByWorkIdDto {

    @ApiModelProperty(value="平台用户ID", required = true)
    private String platformUserId;

//    @ApiModelProperty(value="作品ID", required = true)
//    @NotBlank(message = "作品ID不能为空")
//    private String workId;

    @ApiModelProperty(value="受理号", required = true)
    @NotBlank(message = "受理号不能为空")
    private String acceptanceNumber;
}