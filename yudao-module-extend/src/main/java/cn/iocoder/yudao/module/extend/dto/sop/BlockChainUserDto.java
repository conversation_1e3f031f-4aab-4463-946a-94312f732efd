package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @since 2022--21
 */
@Data
public class BlockChainUserDto {

    @ApiModelProperty(value = "用户类型（0为个人用户，1为企业用户）")
    @NotEmpty(message = "用户类型不能为空")
    private String userType;
    @ApiModelProperty(value = "实名信息")
    @NotEmpty(message = "用户真实姓名/公司名称不能为空")
    private String realName;
    @ApiModelProperty(value = "身份证号码/统一信用")
    @NotEmpty(message = "用户身份证号码/统一信用不能为空")
    private String idNo;
    @ApiModelProperty(value = "用户主键id")
    @NotEmpty(message = "平台用户主键不可为空")
    private String platformUserId;
    @ApiModelProperty(value = "应用密钥")
    @NotEmpty(message = "应用密钥不可为空")
    private String appKey;
}
