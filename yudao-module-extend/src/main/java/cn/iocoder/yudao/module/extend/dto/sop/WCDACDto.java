package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DAC铸造请求数据")
public class WCDACDto {

    @ApiModelProperty(value="发行数量")
    private Long amount;

    @ApiModelProperty(value="类别ID")
    private Long categoryId;

    @ApiModelProperty(value="DAC元数据")
    private String metadata;

    @ApiModelProperty(value="接收地址")
    private String to;

    @ApiModelProperty(value="上链类型")
    private String type;
    /**
     * 在上链平台的用户id
     */
    private Integer blockChainUserId;

}
