package cn.iocoder.yudao.module.extend.vo.copyright;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName IdentifyVo
 */
@Data
@ApiModel
public class PreSignedObjectVo {
    @NotNull
    @ApiModelProperty("附件所属对象类型")
    private String attachmentObjectTypeCode;
    @NotNull
    @ApiModelProperty("附件源文件名称")
    private String originalFilename;
    @NotNull
    @ApiModelProperty("附件大小")
    private String fileSize;
    @NotNull
    @ApiModelProperty("附件描述")
    private String fileDesc;
    @NotNull
    @ApiModelProperty("附件md5")
    private String md5;
    @NotNull
    @ApiModelProperty("子路径")
    private String subPath;


}
