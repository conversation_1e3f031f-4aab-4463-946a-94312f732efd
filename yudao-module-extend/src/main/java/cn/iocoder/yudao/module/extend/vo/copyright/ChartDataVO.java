package cn.iocoder.yudao.module.extend.vo.copyright;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * t统计数据
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
public class ChartDataVO {
private static final long serialVersionUID = 1L;

//----------柱状图数据-------------
  /**
  * 日期
  */
  @ApiModelProperty(value = "日期")
  private String date;
  /**
  * 数量
  */
  @ApiModelProperty(value = "数量")
  private String count;

//  ----------折线图数据-------------

  /**
  * 部门名称列表
  */
  @ApiModelProperty(value = "部门名称列表")
  private List<String> deptNameList;
  /**
   * 日期列表
   */
  @ApiModelProperty(value = "部门名称列表")
  private List<String> dateList;
  /**
   * 数量
   */
  @ApiModelProperty(value = "数量")
  private List<String> countList;

//----------区域报表-------------
  /**
   * 区域名称
   */
  @ApiModelProperty(value = "区域名称")
  private String deptName;
  /**
   * 区域Id
   */
  @ApiModelProperty(value = "区域Id")
  private String deptId;
  /**
   * 当日完成登记数量
   */
  @ApiModelProperty(value = "当日完成登记数量")
  private String dayNum;
  /**
   * 当月完成登记数量
   */
  @ApiModelProperty(value = "当月完成登记数量")
  private String monthNum;
  /**
   * 当年完成登记数量
   */
  @ApiModelProperty(value = "当年完成登记数量")
  private String yearNum;
  /**
   * 存量完成登记数量
   */
  @ApiModelProperty(value = "存量完成登记数量")
  private String stockNum;
  /**
   * 作品类型
   */
  @ApiModelProperty(value = "作品类型")
  private String worksType;
  /**
   * 作品类型名称
   */
  @ApiModelProperty(value = "作品类型名称")
  private String worksTypeName;

}
