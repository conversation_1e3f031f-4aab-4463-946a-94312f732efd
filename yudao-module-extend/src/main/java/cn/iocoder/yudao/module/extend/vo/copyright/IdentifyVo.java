package cn.iocoder.yudao.module.extend.vo.copyright;


import com.baomidou.mybatisplus.annotation.TableField;
import cn.iocoder.yudao.module.extend.entity.copyright.Identify;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName IdentifyVo
 */
@Data
@ApiModel
public class IdentifyVo extends Identify {

    /**
     * 备注
     */
    @TableField(exist = false)
    @ApiModelProperty(value="法人名称")
    private String legalName;

    @TableField(exist = false)
    @ApiModelProperty(value="其他证明文件")
    private String otherFiles;

    @TableField(exist = false)
    @ApiModelProperty(value="身份正面id")
    private String frontAttachId;

    @TableField(exist = false)
    @ApiModelProperty(value="身份背面id")
    private String backAttachId;

    @TableField(exist = false)
    @ApiModelProperty(value="营业执照文件id")
    private String businessAttachId;

    /**
     * 身份证正面
     */
    @TableField(exist = false)
    @ApiModelProperty(value="身份证正面")
    private String idCardFront;
    /**
     * 身份证反面
     */
    @TableField(exist = false)
    @ApiModelProperty(value="身份证反面")
    private String idCardBack;
    /**
     * 营业执照
     */
    @TableField(exist = false)
    @ApiModelProperty(value="营业执照")
    private String businessLicence;



}
