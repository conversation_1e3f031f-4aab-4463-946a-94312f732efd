package cn.iocoder.yudao.module.extend.vo.copyright;

import cn.iocoder.yudao.module.extend.entity.copyright.ApiEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 版权登记列表查询参数
 *
 * <AUTHOR>
 * @date 2023-02-20
 **/
@Data
@ApiModel
public class WorksInstPageQueryParam extends ApiEntity {
    @ApiModelProperty(value = "作品名称")
    private String worksName;
    @ApiModelProperty(value = "首次提交开始时间")
    private String createEndtimeStart;
    @ApiModelProperty(value = "首次提交结束时间")
    private String createEndtimeEnd;
    @ApiModelProperty(value = "更新开始时间")
    private String updateTimeStart;
    @ApiModelProperty(value = "更新结束时间")
    private String updateTimeEnd;
    @ApiModelProperty(value = "著作权人名称")
    private String ownersName;
    @ApiModelProperty(value = "状态")
    private String statusCd;
    @ApiModelProperty(value = "作品类型")
    private String worksType;
    @ApiModelProperty(value = "受理人")
    private String acceptor;


    @ApiModelProperty(value = "区县ID")
    private Long deptId;
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "用户ID",hidden = true)
    private List<Long> userIds;
}
