package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 著作权人
 *
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@TableName("COPYRIGHT_OWNERS")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "著作权人")
public class CopyrightOwners extends Model<CopyrightOwners> {
private static final long serialVersionUID = 1L;

    /**
     * 著作权人ID
     */

    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value="著作权人ID")
    private Long ownerId;
    /**
     * 著作权人类型（个人or企业）
     */
    @ApiModelProperty(value="著作权人类型（个人or企业）")
    @NotBlank(message = "著作权人类型不能为空")
    private String ownerType;
    /**
     * 著作权人名称（个人姓名or企业名称）
     */
    @ApiModelProperty(value="著作权人名称（个人姓名or企业名称）")
    @NotBlank(message = "著作权人名称不能为空")
    private String ownerName;
    /**
     * 著作权人证件ID（身份证号or营业执照编码）
     */
    @ApiModelProperty(value="著作权人证件ID（身份证号or营业执照编码）")
    @NotBlank(message = "证件ID不能为空")
    private String identification;
    /**
     * 手机号码
     */
    @ApiModelProperty(value="手机号码")
    private String phone;
    /**
     * 身份证正面
     */
    @ApiModelProperty(value="身份证正面")
    private String idCardFront;
    /**
     * 身份证反面
     */
    @ApiModelProperty(value="身份证反面")
    private String idCardBack;
    /**
     * 营业执照
     */
    @ApiModelProperty(value="营业执照")
    private String businessLicence;
    /**
     * 证件类型
     */
    @ApiModelProperty(value="证件类型")
    @NotBlank(message = "证件类型不能为空")
    private String papersType;
    /**
     * 署名情况
     */
    @ApiModelProperty(value="署名情况")
    @NotBlank(message = "署名情况不能为空")
    private String signature;
    /**
     * 国籍
     */
    @ApiModelProperty(value="国籍")
    @NotBlank(message = "国籍不能为空")
    private String country;
    /**
     * 省
     */
    @ApiModelProperty(value="省")
    @NotBlank(message = "省不能为空")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty(value="市")
    @NotBlank(message = "市不能为空")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty(value="区")
    private String county;
    /**
     * 状态
     */
    @ApiModelProperty(value="状态")
    private String statusCd;
    /**
     * 状态时间
     */
    @ApiModelProperty(value="状态时间")
    private LocalDateTime statusDate;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    @TableField(fill = FieldFill.UPDATE)
    private String updateStaff;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateDate;
    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value = "身份证明文件")
    private List<AttachmentInfo> attachmentInfoList;

    @ApiModelProperty(value="法人名称")
    private String legalName;

    @ApiModelProperty(value="其他证明文件")
    private String otherFiles;

    @ApiModelProperty(value="身份正面id")
    private String frontAttachId;
    @ApiModelProperty(value="身份背面id")
    private String backAttachId;

    @ApiModelProperty(value="其他文件id")
    private String otherfilesAttachId;

    @ApiModelProperty(value="营业执照文件id")
    private String businessAttachId;


    @ApiModelProperty(value="身份证识别出来的住址")
    private String address;

    @TableField(exist = false)
    @ApiModelProperty(value="版权编号，excel批量导入时使用")
    private String copyrightNo;

    @TableField(exist = false)
    @ApiModelProperty(value="所属账号")
    private String acceptor;

}
