package cn.iocoder.yudao.module.extend.mapper.copyright;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.extend.entity.copyright.BusinessBlockchainLinkDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Collection;
import java.util.List;

/**
 * 业务区块链关联信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BusinessBlockchainLinkMapper extends BaseMapperX<BusinessBlockchainLinkDO> {

    /**
     * 批量获取每个业务ID的最新区块链关联记录
     *
     * @param businessIds 业务ID列表
     * @param businessType 业务类型
     * @return 最新的区块链关联记录列表
     */
    @Select("<script>" +
            "SELECT * FROM (" +
            "  SELECT *, ROW_NUMBER() OVER (PARTITION BY BUSINESS_ID ORDER BY create_time DESC) as rn" +
            "  FROM business_blockchain_link" +
            "  WHERE BUSINESS_ID IN " +
            "  <foreach collection='businessIds' item='businessId' open='(' separator=',' close=')'>" +
            "    #{businessId}" +
            "  </foreach>" +
            "  AND BUSINESS_TYPE = #{businessType}" +
            "  AND deleted = 0" +
            ") t WHERE t.rn = 1" +
            "</script>")
    List<BusinessBlockchainLinkDO> selectLatestLinksByBusinessIdsAndType(@Param("businessIds") Collection<Long> businessIds,
                                                                          @Param("businessType") String businessType);

}
