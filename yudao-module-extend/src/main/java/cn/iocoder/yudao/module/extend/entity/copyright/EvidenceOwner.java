package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@TableName("evidence_owner")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "存证著作权人")
public class EvidenceOwner  extends Model<EvidenceOwner> {
    @TableId(type= IdType.AUTO)
    @ApiModelProperty(value = "主键id", hidden = true)
    private Long ownerId;

//    @ApiModelProperty(value = "著作权人应用场景类型（存证：evidence，登记：registration）", required = true)
//    @NotBlank(message = "著作权人应用场景类型不能为空")
//    private String ownerSceneType;

    @ApiModelProperty(value = "著作权人名称（个人姓名or企业名称）", required = true)
    @NotBlank(message = "著作权人名称不能为空")
    private String ownerName;

    @ApiModelProperty(value = "著作权人证件号码（身份证号or营业执照编码）", required = true)
    @NotBlank(message = "证件号码不能为空")
    private String idNo;

    @ApiModelProperty(value = "著作权人类型（个人: person, 企业: enterprise）", required = true)
    @NotBlank(message = "著作权人类型不能为空")
    private String ownerType;

    @ApiModelProperty(value = "证件类型（身份证：identityCard，营业执照：busiLicense）", required = true)
    @NotBlank(message = "证件类型不能为空")
    private String papersType;

    @ApiModelProperty(value = "法人名称(证件类型为营业执照时必传)")
    private String legalName;

    @ApiModelProperty(value = "署名情况(存证不传，登记著作权人时必传)")
//    @NotBlank(message = "署名情况不能为空")
    private String signature;
    /**
     * 状态
     */
    @ApiModelProperty(value="状态")
    private String statusCd;
    /**
     * 状态时间
     */
    @ApiModelProperty(value="状态时间")
    private LocalDateTime statusDate;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    private String updateStaff;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    private LocalDateTime updateDate;
    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;

    @TableField(exist = false)
    @ApiModelProperty(value="所属账号")
    private String acceptor;
}
