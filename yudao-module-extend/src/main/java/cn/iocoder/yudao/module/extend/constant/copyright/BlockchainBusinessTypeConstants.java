package cn.iocoder.yudao.module.extend.constant.copyright;

/**
 * 区块链业务类型常量
 *
 * <AUTHOR>
 */
public class BlockchainBusinessTypeConstants {

    /**
     * NFT发行
     */
    public static final String NFT_ISSUE = "NFT_ISSUE";

    /**
     * NFT转移
     */
    public static final String NFT_TRANSFER = "NFT_TRANSFER";

    /**
     * 数据存证
     */
    public static final String DATA_CERTIFICATE = "DATA_CERTIFICATE";

    /**
     * 版权登记
     */
    public static final String COPYRIGHT_REGISTER = "COPYRIGHT_REGISTER";

}
