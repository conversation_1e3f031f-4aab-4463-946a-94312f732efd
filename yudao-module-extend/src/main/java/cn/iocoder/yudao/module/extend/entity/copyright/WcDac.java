package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@TableName("wc_dac")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DAC铸造请求数据")
public class WcDac extends Model<WcDac> {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value="id")
    private Long id;

    @ApiModelProperty(value="DAC ID")
    private Long dacId;

    @ApiModelProperty(value="作品ID")
    private Long workId;

    @ApiModelProperty(value="发行数量")
    private Long amount;

    @ApiModelProperty(value="类别ID")
    private Long categoryId;

    @TableField("`to`")
    @ApiModelProperty(value="接收地址")
    private String to;

    @ApiModelProperty(value="DAC状态")
    private String statusCd;

    @ApiModelProperty(value="gas费用")
    private String gasUsed;

    @ApiModelProperty(value="区块高度")
    private Long blockNumber;

    @ApiModelProperty(value="交易哈希")
    private String transactionHash;

    @ApiModelProperty(value="区块hash")
    private String hash;

    @ApiModelProperty(value="修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statusDate;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
}
