package cn.iocoder.yudao.module.extend.enums.copyright;

/**
 * 著作权人证件类型
 *
 * <AUTHOR>
 * @date 2020-10-14
 **/
public enum PaperTypeEnum {

    ID_CARD("identityCard","身份证"),
    BUSINESS_LICENSE("busiLicense","营业执照"),
    ORGANIZATION_CERTIFICATE("organizationCodeCertificate","组织机构代码证书"),
    INSTITUTION_LEGAL_PERSON("institutionAsLegalPerson","事业单位法人证书"),
    MASS_ORGANIZATION("massOrganization","社团法人证书"),
    OTHER_VALID_DOCUMENTS("otherValidDocuments","其他有效证件"),
    RESIDENCE_BOOKLET("residenceBooklet","户口簿");

    PaperTypeEnum(String code, String name){
        this.code=code;
        this.name=name;
    }

    private String code;

    private String name;

    public String getCode(){
        return this.code;
    }

    public static String getName(String code){
        for (PaperTypeEnum ownerType : PaperTypeEnum.values()){
            if (ownerType.getCode().equals(code)){
                return ownerType.name;
            }
        }
        return null;
    }


}
