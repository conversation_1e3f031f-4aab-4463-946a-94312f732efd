package cn.iocoder.yudao.module.extend.controller.sop;


import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.extend.config.sop.SopProperties;
import cn.iocoder.yudao.module.extend.dto.sop.BlockChainUserDto;
import cn.iocoder.yudao.module.extend.dto.sop.WCDACDto;
import cn.iocoder.yudao.module.extend.util.sop.HttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/blockChain")
public class BlockChainController {

    @Autowired
    private HttpUtil httpUtil;
    @Autowired
    private SopProperties sopProperties;

    @ApiOperation(value = "1.用户注册，获取私钥地址和公钥地址", position = 1)
    @PostMapping("/register")
    public JSONObject foreign(@RequestBody @Validated BlockChainUserDto blockChainUserDto) {
        String response = httpUtil.doPost(blockChainUserDto,  false,sopProperties.getBlockchainRegister(), "1.0");
        return JSONUtil.parseObj(response);
    }

    @PostMapping("/blockChinaCount")
    @ApiOperation(value = "12.上链数据", notes = "上链数据",position = 14)
    public JSONObject blockChinaCount(){
        String response = httpUtil.doPost(null,false,sopProperties.getBlockchainBlockChinaCount(), "1.0");
        return JSONUtil.parseObj(response);
    }

    @PostMapping("/issueNft")
    @ApiOperation(value = "6.资产发行", notes = "资产发行",position = 7)
    public JSONObject issueNft(@RequestBody @Validated WCDACDto dac){
        String response = httpUtil.doPost(dac,false,sopProperties.getIssueNft(), "1.0");
        return JSONUtil.parseObj(response);
    }

}
