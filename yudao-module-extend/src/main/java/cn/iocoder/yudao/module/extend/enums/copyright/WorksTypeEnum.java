package cn.iocoder.yudao.module.extend.enums.copyright;


/**
 * <AUTHOR> 102306
 * 作品状态
 */

public enum WorksTypeEnum {
    /** 省版权局状态 */
    WORK_REGISTER_PENDING("2","待受理"),
    WORK_REGISTER_FIRST_TRIAL("4","初审中"),
    WORK_REGISTER_RECHECK("8","复审中"),
    WORK_REGISTER_CERT_MAKE("16","制证中"),
    WORK_REGISTER_COMPLETE("32","登记完成"),
    WORK_REGISTER_FAIL("65536","未通过审核"),

    /**正常*/
    WORK_NORMAL("1000","正常"),
    /**无效*/
    WORK_INVALID("1100","无效"),
    /**草稿*/
    WORK_DRAFT("1200","草稿"),
    /**文件解析*/
    WORK_FILE_ANALYSIS("1300","文件解析"),
    /**监测中*/
    WORK_MONITOR("1400","监测中"),
    /**下架*/
    WORK_OFF_SHELF("1500","下架"),
    /**取证中*/
    WORK_OBTAIN_EVIDENCE("1600","取证中"),
    /**取证完成*/
    WORK_OBTAIN_EVIDENCE_COMPLETE("1700","取证完成"),
    /**初审中*/
    WORK_FIRST_TRIAL("1800","存证审核中"),
    /**审核不通过*/
    WORK_FAIL_PASS("1900","审核不通过"),
    /**复审 上传省版权局的状态*/
    WORK_RECHECK("2000","登记审核中"),
    /**
     * 省局新增公告状态，本地同步添加，并于页面展示
     */
    WORK_NOTICE("2010","公告中"),
    /**预复审*/
    WORK_RECHECK_PRE_RECHECK("2100","预复审"),
    /**复审*/
    WORK_RECHECK_MORE("2200","复审");



    private final String code;
    private final String name;

     WorksTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode(){
        return this.code;
    }

    public String getName(){
        return this.name;
    }

    public static String getNameByCode(String code){
        for(WorksTypeEnum typeEnum: WorksTypeEnum.values()){
            if(code.equals(typeEnum.getCode())){
                return typeEnum.getName();
            }
        }
        return  null;
    }
}
