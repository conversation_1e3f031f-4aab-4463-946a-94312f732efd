package cn.iocoder.yudao.module.extend.entity.copyright;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@TableName("business_blockchain_link")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "业务区块链关联信息")
public class BusinessBlockchainLinkDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 业务资产ID
     */
    @ApiModelProperty(value = "业务资产ID")
    private Long businessId;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 用户钱包地址
     */
    @ApiModelProperty(value = "用户钱包地址")
    private String address;

    /**
     * DAC ID（从区块链返回的 dacIdList 中获取）
     */
    @ApiModelProperty(value = "DAC ID")
    private Integer dacId;

    /**
     * 作品ID（关联的作品）
     */
    @ApiModelProperty(value = "作品ID")
    private Long worksId;

    /**
     * 发行数量
     */
    @ApiModelProperty(value = "发行数量")
    private Long amount;

    /**
     * 类别ID
     */
    @ApiModelProperty(value = "类别ID")
    private Long categoryId;

    /**
     * 接收地址（NFT接收者的区块链地址）
     */
    @TableField("`to`")
    @ApiModelProperty(value = "接收地址")
    private String to;

    /**
     * gas费用（区块链交易消耗的gas）
     */
    @ApiModelProperty(value = "gas费用")
    private String gasUsed;

    /**
     * 区块高度（交易所在的区块号）
     */
    @ApiModelProperty(value = "区块高度")
    private String blockNumber;

    /**
     * 交易哈希（区块链交易的唯一标识）
     */
    @ApiModelProperty(value = "交易哈希")
    private String transactionHash;

    /**
     * 区块hash（区块的哈希值）
     */
    @ApiModelProperty(value = "区块hash")
    private String hash;

    /**
     * 创建时间（业务字段）
     */
    @ApiModelProperty(value = "创建时间（业务字段）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;

    /**
     * 状态更新时间
     */
    @ApiModelProperty(value = "状态更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime statusDate;

    /**
     * DAC状态（从区块链返回的status）
     */
    @ApiModelProperty(value = "DAC状态")
    private String statusCd;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
}
