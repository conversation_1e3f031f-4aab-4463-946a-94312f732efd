package cn.iocoder.yudao.module.extend.util.copyright;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.HexUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestAlgorithm;
import cn.hutool.crypto.digest.Digester;
import com.obs.services.ObsClient;
import com.obs.services.model.*;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.extend.entity.copyright.AttachmentInfo;
import cn.iocoder.yudao.module.extend.entity.copyright.AttachmentObjectType;
import cn.iocoder.yudao.module.extend.enums.copyright.MimeTypeEnum;
import cn.iocoder.yudao.module.extend.exception.copyright.BusinessException;
import cn.iocoder.yudao.module.extend.service.copyright.AttachmentObjectTypeService;
import cn.iocoder.yudao.module.extend.vo.copyright.AttachmentInfoVo;
import cn.iocoder.yudao.module.extend.vo.copyright.PreSignedObjectVo;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.Base64;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;

@Component
@Data
@Slf4j
public class ObsUtil {

    @Value("${extend.obs.access-key}")
    public String ak;

    @Value("${extend.obs.secret-key}")
    public String sk;

    @Value("${extend.obs.endpoint}")
    public String endpoint;

    @Value("${extend.obs.bucket-name}")
    public String BucketName;

    @Value("${extend.obs.down-url}")
    public String downUrl;

    @Value("${extend.obs.expire-seconds}")
    public int expireSeconds;

    @Autowired
    AttachmentObjectTypeService attachmentObjectTypeService;

    @Autowired
    ConfigApi configApi;

    /***
     * 实例化客户端
     * @return
     */
    public ObsClient obsClient() {
        return new ObsClient(ak, sk, endpoint);
    }

    /***
     * 华为云obs文件上传
     */
    public AttachmentInfoVo upload(MultipartFile file, String attachmentObjectTypeCode, String fileDesc,String userId) throws IOException {
        if (StrUtil.isEmpty(userId)){
            Long loginUserId = SecurityFrameworkUtils.getLoginUserId();
            userId = loginUserId != null ? String.valueOf(loginUserId) : null;
        }
        AttachmentObjectType attachmentObjectType = attachmentObjectTypeService.getById(attachmentObjectTypeCode);
        if (attachmentObjectType == null) {
            throw new BusinessException("文件类型不能为空");
        }
        String ext;
        if (Objects.requireNonNull(file.getOriginalFilename()).contains(StrUtil.DOT)) {
            ext = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(StrUtil.DOT) + 1);
        } else {
            ext = Objects.requireNonNull(file.getContentType()).substring(file.getContentType().indexOf(StrUtil.SLASH) + 1);
        }
        ext = ext.toLowerCase();
        if(StrUtil.isNotBlank(ext)){
            if(!fileTypeVerify(ext)){
                throw new BusinessException("不合法的文件类型");
            }
        }
        String mimeType = MimeTypeEnum.getByExtension(ext.toLowerCase()).getMimeType();
        String filePath = attachmentObjectType.getAttachmentObjectTypeName()+StrUtil.SLASH+UUID.fastUUID() + StrUtil.DOT + ext;
        ObsClient client = obsClient();
        InputStream input = file.getInputStream();
        PutObjectResult result = client.putObject(BucketName, filePath, input);
//        log.info("文件上传返回数据：{}" + result.getObjectUrl());
        AttachmentInfo attachmentInfo = new AttachmentInfo();
        attachmentInfo.setCreateStaff(Integer.valueOf(userId));
        attachmentInfo.setCreateDate(LocalDateTime.now());
        attachmentInfo.setStatusDate(LocalDateTime.now());
        attachmentInfo.setStatusCd("1000");
        attachmentInfo.setAttachmentObjectTypeCode(attachmentObjectTypeCode);
        attachmentInfo.setFileDesc(fileDesc);
        attachmentInfo.setFileName(file.getOriginalFilename());
        attachmentInfo.setFileSize(String.valueOf(file.getSize()));
        attachmentInfo.setMimeType(mimeType);
        attachmentInfo.setFilePath(StrUtil.SLASH + filePath);

        Digester md5 = new Digester(DigestAlgorithm.MD5);
        byte[] bytes = md5.digest(file.getInputStream(), IoUtil.DEFAULT_MIDDLE_BUFFER_SIZE);
        String hashCode = HexUtil.encodeHexStr(bytes);
        attachmentInfo.setHashCode(hashCode);

        boolean b = attachmentInfo.insert();
        if (b) {
            attachmentInfo.setAttachId(attachmentInfo.getAttachId());
            AttachmentInfoVo attachmentInfoVo = new AttachmentInfoVo();
            BeanUtil.copyProperties(attachmentInfo, attachmentInfoVo);
            attachmentInfoVo.setFileUrl(downUrl+StrUtil.SLASH + filePath);
            return attachmentInfoVo;
        } else {
            throw BusinessException.resourceCreateException();
        }
    }

    public CommonResult getPreSignedUploadUrl(PreSignedObjectVo preSignedObjectVo){
        try {
            AttachmentObjectType attachmentObjectType = attachmentObjectTypeService.getById(preSignedObjectVo.getAttachmentObjectTypeCode());
            if (attachmentObjectType == null) {
                throw new BusinessException("文件类型code不正确");
            }
            String subPath = preSignedObjectVo.getSubPath() != null ? preSignedObjectVo.getSubPath() : "";
            if (subPath.startsWith(StrUtil.SLASH)) {
                subPath = subPath.substring(1);
            }
            if (subPath.endsWith(StrUtil.SLASH)) {
                subPath = subPath.substring(0, subPath.length() - 1);
            }
            if (StrUtil.isNotBlank(subPath)) {
                subPath  = subPath + StrUtil.SLASH;
            }
            String ext;
            if (Objects.requireNonNull(preSignedObjectVo.getOriginalFilename()).contains(StrUtil.DOT)) {
                ext = preSignedObjectVo.getOriginalFilename().substring(preSignedObjectVo.getOriginalFilename().lastIndexOf(StrUtil.DOT) + 1);
            } else {
                ext = "";
            }
            ext = ext.toLowerCase();
            if(StrUtil.isNotBlank(ext)){
                if(!fileTypeVerify(ext)){
                    throw new BusinessException("不合法的文件类型");
                }
            }


            String mimeType = MimeTypeEnum.getByExtension(ext.toLowerCase()).getMimeType();
            String minioFileName = attachmentObjectType.getAttachmentObjectTypeName()+StrUtil.SLASH+subPath + UUID.fastUUID() + StrUtil.DOT + ext;

            String md5Base64 = Base64.getEncoder().encodeToString(HexUtil.decodeHex(preSignedObjectVo.getMd5()));

            long expireSeconds = 600L;
            Map<String, String> headers = new HashMap<>();
//            String contentType = "text/plain";
            String contentType = "application/octet-stream";
            headers.put("Content-Type", contentType);
//            TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.PUT, expireSeconds);
//            request.setBucketName(attachmentObjectType.getAttachmentObjectTypeName());
//            request.setObjectKey(minioFileName);
//            request.setHeaders(headers);
            TemporarySignatureRequest request = new TemporarySignatureRequest(HttpMethodEnum.PUT,BucketName,minioFileName,null,expireSeconds);
            request.setHeaders(headers);
            ObsClient client = obsClient();
            TemporarySignatureResponse response = client.createTemporarySignature(request);
            log.info(response.toString());
            String signedUrl=response.getSignedUrl();
            AttachmentInfo attachmentInfo = new AttachmentInfo();
//            attachmentInfo.setCreateStaff(SecurityUtils.getUser().getId());
            attachmentInfo.setCreateDate(LocalDateTime.now());
            attachmentInfo.setStatusDate(LocalDateTime.now());
            attachmentInfo.setStatusCd("1000");
            attachmentInfo.setAttachmentObjectTypeCode(preSignedObjectVo.getAttachmentObjectTypeCode());
            attachmentInfo.setFileDesc(preSignedObjectVo.getFileDesc());
            attachmentInfo.setFileName(preSignedObjectVo.getOriginalFilename());
            attachmentInfo.setFileSize(String.valueOf(preSignedObjectVo.getFileSize()));
            attachmentInfo.setMimeType(mimeType);
            attachmentInfo.setFilePath(StrUtil.SLASH + minioFileName );
            attachmentInfo.setHashCode(preSignedObjectVo.getMd5());
            attachmentInfo.setRemark("presigned");
            boolean b = attachmentInfo.insert();
            if (b) {
                attachmentInfo.setAttachId(attachmentInfo.getAttachId());
                AttachmentInfoVo attachmentInfoVo = new AttachmentInfoVo();
                BeanUtil.copyProperties(attachmentInfo, attachmentInfoVo);
                attachmentInfoVo.setFileUrl(signedUrl);
                attachmentInfoVo.setFileUrl(signedUrl);
//                attachmentInfoVo.set(md5Base64);
                return CommonResult.success(attachmentInfoVo);
            } else {
                throw BusinessException.resourceCreateException();
            }
        }catch (Exception e){
            return CommonResult.error(500, e.getMessage());
        }
    }



    public String createTemporarySignature(String objectKey){
        TemporarySignatureRequest request = new TemporarySignatureRequest();
        request.setBucketName(BucketName);
        request.setObjectKey(objectKey);
        request.setRequestDate(new Date());
        ObsClient client = obsClient();
        TemporarySignatureResponse response = client.createTemporarySignature(request);
        log.info(response.toString());
        String signedUrl=response.getSignedUrl();
        return signedUrl;
    }

    public InputStream getObject(String objectKey){
        try {
            ObsClient obsClient = obsClient();
            ObsObject obsObject = obsClient.getObject(BucketName, objectKey);
            return obsObject.getObjectContent();
        }catch (Exception e){
            log.info(e.getMessage());
            return null;
        }
    }

    /***
     * 检查文件是否是允许上传的格式
     */
    public boolean fileTypeVerify(String fileType){
        String fileTypes = configApi.getConfigValueByKey("FILE_TYPE_FILTER");
        if (fileTypes == null) {
            return true;
        } else {
            String[] types = fileTypes.split(",");
            return Arrays.asList(types).contains(fileType.toLowerCase());
        }
    }
}
