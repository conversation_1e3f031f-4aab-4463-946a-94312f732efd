package cn.iocoder.yudao.module.extend.vo.copyright;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统计数据
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
@ExcelIgnoreUnannotated // 只导出有@ExcelProperty注解的字段
public class CustomReportVO {
    private static final long serialVersionUID = 1L;

    @ExcelProperty("日期") // 修正：应该是"日期"而不是"用户名称"
    @ApiModelProperty(value = "日期")
    private String date;

    @ExcelProperty("区域名称")
    @ApiModelProperty(value = "区域名称")
    private String deptName;

    @ExcelProperty("区域ID")
    @ApiModelProperty(value = "区域ID")
    private String deptId;

    @ExcelProperty("作品类型")
    @ApiModelProperty(value = "作品类型")
    private String worksType;

    @ExcelProperty("作品类型名称")
    @ApiModelProperty(value = "作品类型名称")
    private String worksTypeName;

    @ExcelProperty("数量")
    @ApiModelProperty(value = "数量")
    private String count;
}
