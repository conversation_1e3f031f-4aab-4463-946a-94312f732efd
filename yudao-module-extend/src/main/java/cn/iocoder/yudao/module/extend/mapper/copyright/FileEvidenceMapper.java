package cn.iocoder.yudao.module.extend.mapper.copyright;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.iocoder.yudao.module.extend.entity.copyright.FileEvidence;
import cn.iocoder.yudao.module.extend.vo.copyright.WorksInstPageQueryParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FileEvidenceMapper extends BaseMapper<FileEvidence> {

    List<FileEvidence> getWorksInstPage(@Param("queryParam")WorksInstPageQueryParam queryParam);
}
