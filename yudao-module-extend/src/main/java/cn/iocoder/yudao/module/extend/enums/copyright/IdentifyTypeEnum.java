package cn.iocoder.yudao.module.extend.enums.copyright;

/**
 * 著作权人证件类型
 *
 * <AUTHOR>
 * @date 2020-10-14
 **/
public enum IdentifyTypeEnum {

    PERSON("person","自然人"),
    ENTERPRISE("enterprise","企业法人"),
    OFFICIAL("official","机关法人"),
    INSTITUTION("institution","事业单位法人"),
    SOCIAL("social","社团法人证书"),
    OTHER_ORGANIZATION("otherOrganizations","其他有效证件");

    IdentifyTypeEnum(String code, String name){
        this.code=code;
        this.name=name;
    }

    private String code;

    private String name;

    public String getCode(){
        return this.code;
    }

    public static String getName(String code){
        for (IdentifyTypeEnum ownerType : IdentifyTypeEnum.values()){
            if (ownerType.getCode().equals(code)){
                return ownerType.name;
            }
        }
        return null;
    }


}
