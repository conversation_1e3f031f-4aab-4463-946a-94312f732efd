package cn.iocoder.yudao.module.extend.entity.copyright;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ApiEntity {

    //    @NotNull(message = "时间戳不能为空")
    @ApiModelProperty(value = "时间戳",hidden = true)
    private Long timestamp;

    //    @NotNull(message = "加密参数不能为空")
    @ApiModelProperty(value = "加密参数",hidden = true)
    private String token;
    //    @NotNull(message = "登记机构代码不能为空")
    @ApiModelProperty(value = "登记机构代码",hidden = true)
    private String orgCode;
}
