package cn.iocoder.yudao.module.extend.controller.sop;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import cn.iocoder.yudao.module.extend.config.sop.SopProperties;
import cn.iocoder.yudao.module.extend.dto.sop.*;
import cn.iocoder.yudao.module.extend.util.sop.HttpUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
@Slf4j
@RestController
@RequestMapping("/evidenceAndRegister")
public class EvidenceAndRegisterController {

    @Autowired
    private HttpUtil httpUtil;
    @Autowired
    private SopProperties sopProperties;

    @ApiOperation(value = "1.商户登记存证注册", position = 1)
    @PostMapping("/foreign")
    public JSONObject foreign(@RequestBody @Validated RegisterForeignDto foreignDto) {
        String response = httpUtil.doPost(foreignDto,  false,sopProperties.getRegisterForeign(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "2.文件上传", position = 2)
    @PostMapping("/fileUpload")
    public JSONObject fileUpload(@RequestBody @Validated AttachmentUploadDto attachmentUploadDto) {
        File file = attachmentUploadDto.getFile();
        attachmentUploadDto.setFile(null);
        String response = httpUtil.doPostUpload(attachmentUploadDto,  false,sopProperties.getFileEvidenceFileUpload(), "1.0",file);
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "3.新增著作权人", position = 3)
    @PostMapping("/addOwner")
    public JSONObject addOwner(@RequestBody @Validated OwnerDto ownerDto) {
        String response = httpUtil.doPost(ownerDto,  false,sopProperties.getFileEvidenceAddOwner(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/getOwnersList")
    @ApiOperation(value = "4.查询著作权人列表", position = 4)
    public JSONObject getOwnersList(@Validated OwnersListDto ownersListDto) {
        String response = httpUtil.doGet(ownersListDto,  false,sopProperties.getOwnersGetOwnersList(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/getOwnersById")
    @ApiOperation(value = "5.查询通过id著作权人", position = 5)
    public JSONObject getOwnersById(@Validated OwnersByIdDto ownersByIdDto) {
        String response = httpUtil.doGet(ownersByIdDto,  false,sopProperties.getOwnersGetOwnersById(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

//    @ApiOperation(value = "6.通过id删除著作权人", position = 6)
//    @DeleteMapping("/removeOwnersById")
//    public JSONObject removeOwnersById(@RequestBody @Validated OwnersByIdDto ownersByIdDto) {
//        String response = httpUtil.doPost(ownersByIdDto,  false,sopProperties.getOwnersDelete(), "1.0");
//        JSONObject result = JSONUtil.parseObj(response);
//        return result;
//    }

    @ApiOperation(value = "6.字典查询", position = 6)
    @GetMapping("/dict")
    public JSONArray getDictByType(@Validated DictItemDto dictItemDto) {
        String response = httpUtil.doGet(dictItemDto,  true,sopProperties.getWorkDict(), "1.0");
        JSONArray result = JSONUtil.parseArray(response);
        return result;
    }

    @ApiOperation(value = "7.登记-新增登记作品信息", notes = "保存登记作品信息", position = 7)
    @PostMapping("/workSave")
    public JSONObject workSave(@RequestBody @Validated WorksInstDto worksInstDto) {
        String response = httpUtil.doPost(worksInstDto,  false,sopProperties.getWorkSave(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "8.登记-更新登记作品信息", notes = "更新登记作品信息", position = 8)
    @PostMapping("/workUpdate")
    public JSONObject workUpdate(@RequestBody @Validated WorksInstDto worksInstDto) {
        String response = httpUtil.doPost(worksInstDto,  false,sopProperties.getWorkUpdate(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "9.登记-查询登记作品列表", notes = "查询登记作品列表", position = 9)
    @GetMapping("/workPage")
    public JSONObject getWorksInstPage(@Validated WorkInstPageQueryDto workInstPageQueryDto) {
        String response = httpUtil.doGet(workInstPageQueryDto,  false,sopProperties.getWorkPage(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "10.登记-根据受理号查询登记作品", notes = "查询登记作品信息", position = 10)
    @GetMapping("/getWorkByAcceptNo")
    public JSONObject getWorkByAcceptNo(@Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getWorkGetWorkByAcceptNo(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "11.登记-根据受理号查询审核进度", notes = "根据受理号查询审核进度", position = 11)
    @GetMapping("/getWorkProgressByAcceptNo")
    public JSONObject getWorkProgressByAcceptNo(@Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getWorkProgress(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/worksEvidence")
    @ApiOperation(value = "12.登记-下载作品存证证书", notes = "下载作品存证证书", position = 12)
    public JSONObject depositWorksCertificate(@Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getWorkEvidence(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/worksRegistration")
    @ApiOperation(value = "13.登记-下载作品登记证书", notes = "下载作品登记证书", position = 13)
    public JSONObject registrationWorksCertificate(@Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getWorkRegistration(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/worksRegistrationData")
    @ApiOperation(value = "13.登记-获取作品登记数据", notes = "获取作品登记数据", position = 13)
    public JSONObject WorksRegistrationData(@Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getWorkRegistrationData(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "14.新增存证", notes = "新增存证", position = 14)
    @PostMapping("/baseSave")
    public JSONObject save(@RequestBody @Validated FileEvidenceDto fileEvidenceDto) throws IOException {
        String response = httpUtil.doPost(fileEvidenceDto,  false,sopProperties.getFileEvidenceBaseSave(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "15.存证-查询存证作品列表", notes = "查询存证作品列表", position = 15)
    @GetMapping("/evidencePage")
    public JSONObject getFileEvidencePage(@Validated FileEvidenceListDto evidenceListDto,Page page) {
        String response = httpUtil.doGet(evidenceListDto,  false,sopProperties.getFileEvidencePage(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @ApiOperation(value = "16.存证-查询存证作品详情", notes = "查询存证作品详情", position = 16)
    @GetMapping("/evidenceInfo")
    public JSONObject getFileEvidenceInfo( @Validated WorkByAcceptNoDto workByAcceptNoDto) {
        String response = httpUtil.doGet(workByAcceptNoDto,  false,sopProperties.getFileEvidenceInfo(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @GetMapping("/evidence")
    @ApiOperation(value = "17.存证-下载作品存证证书", notes = "下载作品存证证书", position = 17)
    public JSONObject depositCertificate(@Validated FileEvidenceByWorkIdDto workIdDto) {
        String response = httpUtil.doGet(workIdDto,  false,sopProperties.getFileEvidenceEvidence(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

    @PostMapping("/verification")
    @ApiOperation(value = "18.核验-统一证据编号核验", notes = "统一证据编号核验", position = 18)
    public JSONObject verificationWorkHsh(@RequestBody @Validated worksHashVerificationDto worksHashVerificationDto) {
        String response = httpUtil.doPost(worksHashVerificationDto,  false,sopProperties.getVerificationWorkHash(), "1.0");
        JSONObject result = JSONUtil.parseObj(response);
        return result;
    }

}
