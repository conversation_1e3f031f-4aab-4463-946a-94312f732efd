package cn.iocoder.yudao.module.extend.service.copyright;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.iocoder.yudao.module.extend.entity.copyright.AttachmentObjectType;
import cn.iocoder.yudao.module.extend.mapper.copyright.AttachmentObjectTypeMapper;
import cn.iocoder.yudao.module.extend.service.copyright.AttachmentObjectTypeService;
import org.springframework.stereotype.Service;

/**
 *
 *
 * <AUTHOR>
 * @date 2019-09-27 16:00:17
 */
@Service
public class AttachmentObjectTypeServiceImpl extends ServiceImpl<AttachmentObjectTypeMapper, AttachmentObjectType> implements AttachmentObjectTypeService {

}
