package cn.iocoder.yudao.module.extend.enums.copyright;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * Created by zeb on 2019-05-12.
 */
@Getter
@AllArgsConstructor
public enum BusinessCodeEnum {

    /**
     * 资源新增失败
     */
    RESOURCE_CREATE_EXCEPTION(4001, "资源新增失败"),

    /**
     * 资源不存在
     */
    RESOURCE_NOT_FOUND_EXCEPTION(4002, "资源不存在"),

    /**
     * 资源删除失败
     */
    RESOURCE_DELETE_EXCEPTION(4003, "资源删除失败"),

    /**
     * 资源更新失败
     */
    RESOURCE_UPDATE_EXCEPTION(4004, "资源更新失败");

    private final Integer code;

    private final String description;
}
