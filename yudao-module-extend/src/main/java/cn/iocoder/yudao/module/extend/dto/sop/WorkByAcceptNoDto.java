package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author：we<PERSON>ian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "通过受理号查询作品")
public class WorkByAcceptNoDto {

    @ApiModelProperty(value = "受理号", required = true)
    @NotBlank(message = "受理号不能为空")
    private String acceptNo;

    @ApiModelProperty(value = "平台用户主键id", required = true)
    @NotBlank(message = "平台用户主键id不能为空")
    private String platformUserId;

    public WorkByAcceptNoDto(String acceptNo, String platformUserId) {
        this.acceptNo=acceptNo;
        this.platformUserId=platformUserId;
    }


}