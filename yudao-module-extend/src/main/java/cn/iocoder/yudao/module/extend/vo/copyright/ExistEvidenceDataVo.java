package cn.iocoder.yudao.module.extend.vo.copyright;

import cn.iocoder.yudao.module.extend.entity.copyright.CopyrightOwners;
import lombok.Data;

import java.util.List;

/**
 * @ClassName ExistEvidenceVO
 * <AUTHOR> 102306
 * @Date 2021/7/2 18:22
 */
@Data
public class ExistEvidenceDataVo {
    private String id;
    private String relId;
    private String workName;
    private String legalName;
    private String blockchainId;
    private String blockChainTime;
    private String fileHash;
    private String fileName;//文件名称
    private String fileType;
    private String fileTypeName;
    private String obtainTime;
    private String tsaId;//时间戳id
    private String tsaTime;//
    private String txId;
    private String fileId;
    private String evidenceId;
    private String acceptanceNumber;
    private String idNo;
    private String licenseNo;
    private String bcHash;
    private String userName;
    private Integer userId;
    private Integer fileCount;
    private String filePath;
    private String ownerNames;
    private String worksAuthName;
    private String statusCd;
    private Integer createStaff;
    private List<CopyrightOwners> Owners;
    private String downloadUrl;
    private String today ;
    private String isbn;
}
