package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 *
 *
 * <AUTHOR>
 * @date 2019-09-27 16:00:17
 */
@Data
@ApiModel
@TableName("attachment_object_type")
@EqualsAndHashCode(callSuper = true)
public class AttachmentObjectType extends Model<AttachmentObjectType> {
private static final long serialVersionUID = 1L;

  /**
   * 附件所属对象类型
   */
  @TableId(type= IdType.AUTO)
  @ApiModelProperty(value = "附件所属对象类型")
  private String attachmentObjectTypeCode;

  /**
   * 附件所属对象类型名称
   */
  @ApiModelProperty(value = "附件所属对象类型名称")
  private String attachmentObjectTypeName;

  /**
   * 附件所属对象类型说明
   */
  @ApiModelProperty(value = "附件所属对象类型说明")
  private String attachmentObjectTypeRemark;

  /**
   * 1000：有效状态  1100：无效状态 1200：已处理
   */
  @ApiModelProperty(value = "1000：有效状态  1100：无效状态 1200：已处理")
  private String statusCd;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private LocalDateTime statusDate;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private Integer createStaff;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private LocalDateTime createDate;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private Integer updateStaff;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private LocalDateTime updateDate;

  /**
   *
   */
  @ApiModelProperty(value = "")
  private String remark;

}
