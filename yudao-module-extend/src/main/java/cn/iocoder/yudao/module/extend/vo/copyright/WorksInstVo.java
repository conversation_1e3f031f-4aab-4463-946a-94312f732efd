package cn.iocoder.yudao.module.extend.vo.copyright;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.iocoder.yudao.module.extend.entity.copyright.AttachmentInfo;
import cn.iocoder.yudao.module.extend.entity.copyright.CopyrightOwners;
import cn.iocoder.yudao.module.extend.entity.copyright.WorksAuthor;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 作品指丛书类每本，视频集每集，漫画丛书每本、影集每本、图片、单本小说等，其著作权、出版权都可能有所不同
 * <AUTHOR>
 * @date 2020-10-14 14:13:52
 */
@Data
@ApiModel(value = "作品表")
public class WorksInstVo {
private static final long serialVersionUID = 1L;

    /**
     * 作品ID，主键
     */
    @TableId
    @ApiModelProperty(value="作品ID")
    private Long worksId;
    /**
     * 作品编码
     */
    @ApiModelProperty(value="受理号")
    private String acceptanceNumber;
    /**
     * 作品名称
     */
    @ApiModelProperty(value="作品名称",required = true)
    private String worksName;
    /**
     * 作品类型
     */
    @ApiModelProperty(value="作品类型")
    private String worksType;
    /**
     * 作品类型说明
     */
    @ApiModelProperty(value="作品类型说明")
    private String firstLevelName;
    @ApiModelProperty(value="样本文件ID，多个以逗号隔开")
    private String sampleFileId;
    /**
     * 权力保证书ID
     */
    @ApiModelProperty(value="权力保证书ID")
    private String powerGuaranteeId;
    /**
     * 其他证明材料ID，多个以逗号隔开
     */
    @ApiModelProperty(value="其他证明材料ID，多个以逗号隔开")
    private String otherMaterialsId;
    /**
     * 作品类型说明
     */
    @ApiModelProperty(value="作品类型说明")
    private String firstLevelCode;
    /**
     * 审核意见
     */
    @ApiModelProperty(value="审核意见")
    private String auditOpinion;

    @ApiModelProperty(value="省版权局审核意见")
    private String scAuditOpinion;
    /**
     * 作品封面
     */
    @ApiModelProperty(value="作品封面")
    private String worksCover;
    /**
     * 创作完成地点
     */
    @ApiModelProperty(value="创作完成地点")
    private String createPlace;
    /**
     * 首次发表日期
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value="首次发表日期")
    private Date publishDate;
    /**
     * 发表地点
     */
    @ApiModelProperty(value="发表地点")
    private String publishPlace;

    /**
     * 作品简介
     */
    @ApiModelProperty(value="作品简介")
    private String worksAbstract;
    /**
     * 创作性质
     */
    @ApiModelProperty(value="创作性质")
    private String inditeNature;
    /**
     * 创作完成时间
     */
    @JsonFormat(pattern="yyyy-MM-dd")
    @ApiModelProperty(value="创作完成时间")
    private Date inditeDoneTime;
    /**
     * 作品归属情况
     */
    @ApiModelProperty(value="作品归属情况")
    private String worksBelongType;

//    /**
//     * 链上id
//     */
//    @ApiModelProperty(value="链上id")
//    private Long blockchainId;


    /**
     * 权利取得方式
     */
    @ApiModelProperty(value="权利取得方式")
    private String copyrightObtainChannel;
    /**
     * 分发权利
     */
    @ApiModelProperty(value="分发权利")
    private String copyrightDispense;
    /**
     * 作品署名
     */
    @ApiModelProperty(value="作品署名")
    private String worksSignature;
    /**
     * 权利拥有方式
     */
    @ApiModelProperty(value="权利拥有方式")
    private String copyrightOwnRange;

    private String copyrightOwnRangeList;

    /**
     * 作品HASH值
     */
    @ApiModelProperty(value="作品HASH值")
    private String worksHash;

    /**
     * 作品上链id
     */
    @ApiModelProperty(value="作品上链id")
    private Long blockchainId;

    /**
     * 是否区块链存证
     */
    @ApiModelProperty(value="是否区块链存证(0:是，1:否)")
    private String isDeposited;

    /**
     * 获证时间
     */
    @ApiModelProperty(value="获证时间")
    private LocalDateTime obtainedTime;

    /**
     * 受理区域
     */
    @ApiModelProperty(value="受理区域")
    private String acceptOrg;
    /**
     * 作品状态
     */
    @ApiModelProperty(value="作品状态")
    private String statusCd;
    /**
     * 状态时间
     */
    @ApiModelProperty(value="状态时间")
    private LocalDateTime statusDate;
    /**
     * 创建人
     */
    @ApiModelProperty(value="创建人")
    @TableField(fill = FieldFill.INSERT)
    private String createStaff;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createDate;
    /**
     * 修改人
     */
    @ApiModelProperty(value="修改人")
    @TableField(fill = FieldFill.UPDATE)
    private String updateStaff;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间")
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateDate;
    /**
     * 备注
     */
    @ApiModelProperty(value="备注")
    private String remark;


    @TableField(exist = false)
    @ApiModelProperty(value="著作权人id")
    private List<Long> ownersIds;


    @TableField(exist = false)
    @ApiModelProperty(value="著作权人列表")
    private List<CopyrightOwners> ownersList;

    @ApiModelProperty(value="样本文件列表")
    private List<AttachmentInfo> sampleFileList;

    @ApiModelProperty(value="权力保证书")
    private AttachmentInfo powerGuarantee;

    @ApiModelProperty(value="其他证明材料列表")
    private List<AttachmentInfo> otherMaterialsList;

    @TableField(exist = false)
    @ApiModelProperty(value = "作者集合")
    private List<WorksAuthor> worksInstAuthors;

    @TableField(exist = false)
    @ApiModelProperty(value="是否是草稿")
    private Boolean isDraft;


    }
