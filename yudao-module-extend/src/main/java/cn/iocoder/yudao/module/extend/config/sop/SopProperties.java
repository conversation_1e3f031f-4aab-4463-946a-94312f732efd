package cn.iocoder.yudao.module.extend.config.sop;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023-03-21 11:41
 */
@Data
@Component
@ConfigurationProperties(prefix = "extend.sop")
public class SopProperties {
    @ApiModelProperty(value = "是否启用")
    private Boolean enabled;

    @ApiModelProperty(value = "访问路径")
    private String accessUrl;

    @ApiModelProperty(value = "平台应用id")
    private String appKey;

    @ApiModelProperty(value = "应用私钥")
    private String privateKey;

    @ApiModelProperty(value = "应用公钥")
    private String publicKey;

    @ApiModelProperty(value = "平台公钥")
    private String platformPublicKey;

    @ApiModelProperty(value = "商户登记存证注册")
    private String registerForeign;

    @ApiModelProperty(value = "文件上传")
    private String fileEvidenceFileUpload;

    @ApiModelProperty(value = "新增著作权人")
    private String fileEvidenceAddOwner;

    @ApiModelProperty(value = "查询著作权人列表")
    private String ownersGetOwnersList;

    @ApiModelProperty(value = "查询著作权人详情")
    private String ownersGetOwnersById;

    @ApiModelProperty(value = "通过id删除著作权人")
    private String ownersDelete;

    @ApiModelProperty(value = "字典查询")
    private String workDict;

    @ApiModelProperty(value = "登记-新增登记作品信息")
    private String workSave;

    @ApiModelProperty(value = "登记-更新登记作品信息")
    private String workUpdate;

    @ApiModelProperty(value = "登记-查询登记作品列表")
    private String workPage;

    @ApiModelProperty(value = "登记-根据受理号查询登记作品")
    private String workGetWorkByAcceptNo;

    @ApiModelProperty(value = "登记-根据受理号查询审核进度")
    private String workProgress;

    @ApiModelProperty(value = "登记-下载作品存证证书")
    private String workEvidence;

    @ApiModelProperty(value = "登记-下载作品登记证书")
    private String workRegistration;

    @ApiModelProperty(value = "登记-获取作品登记数据")
    private String workRegistrationData;

    @ApiModelProperty(value = "存证-新增存证")
    private String fileEvidenceBaseSave;

    @ApiModelProperty(value = "存证-查询存证作品列表")
    private String fileEvidencePage;

    @ApiModelProperty(value = "存证-查询存证作品详情")
    private String fileEvidenceInfo;

    @ApiModelProperty(value = "存证-下载作品存证证书")
    private String fileEvidenceEvidence;

    @ApiModelProperty(value = "核验-统一证据编号核验")
    private String verificationWorkHash;

    @ApiModelProperty(value = "用户注册，获取私钥地址和公钥地址")
    private String blockchainRegister;

    @ApiModelProperty(value = "文创链数据")
    private String blockchainBlockChinaCount;

    @ApiModelProperty(value = "资产发行")
    private String issueNft;
}
