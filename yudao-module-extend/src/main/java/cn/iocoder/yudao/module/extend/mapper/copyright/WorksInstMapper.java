package cn.iocoder.yudao.module.extend.mapper.copyright;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.iocoder.yudao.module.extend.entity.copyright.WorksInst;
import cn.iocoder.yudao.module.extend.vo.copyright.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022--24
 */
@Mapper
public interface WorksInstMapper extends BaseMapper<WorksInst> {

    List<WorksInst> getWorksInstPage(@Param("queryParam") WorksInstPageQueryParam queryParam);

    WorksInst getOldWorksInst(@Param("worksId")Long worksId);

    List<StatisticalDataVO> getStatusNum();

    StatisticalDataVO getUserCount();

    List<ChartDataVO> getInsertForDay(@Param("deptId")Long deptId,@Param("dateList")List<String> dateList);

    List<DeptVO> getDeptListById(@Param("id")String id);

    List<ChartDataVO> getRegisterForDay(@Param("deptId")Long deptId,@Param("dateList") List<String> dateList);

    List<ChartDataVO> regionalReport(@Param("deptIdList")List<Long> deptIdList, @Param("type")String statisticsDay);

    List<ChartDataVO> typeReport(@Param("type")String statisticsDay);

    List<CustomReportVO> customReport(@Param("deptIds")List<String> deptIds,@Param("dateList") List<String> dateList, @Param("format")String format, @Param("worksType")List<String> list);

    @Select("SELECT\n" +
            "\tw.* \n" +
            "FROM\n" +
            "\tworks_inst AS w\n" +
            "\tLEFT JOIN works_register AS r ON w.WORKS_ID = r.works_id \n" +
            "WHERE\n" +
            "\tw.STATUS_CD = '1400' \n" +
            "\tAND r.accept_no IS NULL")
    List<WorksInst> getWorksToSc();
}
