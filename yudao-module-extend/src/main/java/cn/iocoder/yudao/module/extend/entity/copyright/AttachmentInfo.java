package cn.iocoder.yudao.module.extend.entity.copyright;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 实名认证
 *
 * <AUTHOR>
 * @date 2020-10-29 15:48:38
 */
@Data
@TableName("attachment_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "附件")
public class AttachmentInfo extends Model<AttachmentInfo> {
private static final long serialVersionUID = 1L;

    @TableId(type= IdType.AUTO)
    @ApiModelProperty("附件ID")
    private Long attachId;
    @ApiModelProperty("附件所属对象类型")
    private String attachmentObjectTypeCode;
    @ApiModelProperty("附件名称")
    private String fileName;
    @ApiModelProperty("MIME类型")
    private String mimeType;
    @ApiModelProperty("附件大小")
    private String fileSize;
    @ApiModelProperty("附件描述")
    private String fileDesc;
    @ApiModelProperty("上传时间")
    private LocalDateTime createDate;
    @ApiModelProperty("附件路径")
    private String filePath;
    @ApiModelProperty("附件访问地址")
    @TableField(
            exist = false
    )
    private String fileUrl;
    @ApiModelProperty("图片缩略图路径，类型为图片时有值")
    private String fileScalePath;
    @ApiModelProperty("文件哈希值")
    private String hashCode;
    @ApiModelProperty("1000：有效状态  1100：无效状态 1200：已处理")
    private String statusCd;
    @ApiModelProperty("")
    private LocalDateTime statusDate;
    @ApiModelProperty("")
    private Integer createStaff;
    @ApiModelProperty("")
    private Integer updateStaff;
    @ApiModelProperty("")
    private LocalDateTime updateDate;
    @ApiModelProperty("")
    private String remark;
    }
