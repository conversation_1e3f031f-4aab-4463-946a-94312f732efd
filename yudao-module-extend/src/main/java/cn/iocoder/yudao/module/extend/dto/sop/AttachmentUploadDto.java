package cn.iocoder.yudao.module.extend.dto.sop;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.File;

/**
 * @author：weiqian
 * @Date：2022/7/26 15:31
 */
@Data
@ApiModel(value = "文件上传")
public class AttachmentUploadDto {

    @ApiModelProperty(value="文件", required = true)
    private File file;

    /**
     * 类型存储于数据库的“comm_inst_attachment_object_type”表中
     * VJS：vjs-video-source,VJS视频文件
     * authorityGuarantee：copyright-auth,权力保证书
     * checkFileHash：copyright-contrast,文件对比
     * chinaumsFiles：copyright-chinaums,银联相关图片
     * collectionCover：bmark-collection-cover,藏品封面
     * collectionSampleFiless：bmark-collection-source,藏品样本文件
     * evidenceDocument：copyright-evidence，取证文件
     * feedbackImg：bmark-feedback，意见反馈图片
     * identifyCover：bmark-cover，用户头像
     * identityDocument：copyright-auth、身份证明文件
     * otherMaterial：copyright-other、其他材料
     * ownerSampleFiless：copyright-auth、著作权人其他材料
     * sampleFiless：copyright-source、样本文件
     * videoAuthFiless：bmark-video-auth、视频市场授权声明文件
     * videoInterceptImg：bmark-video-img、视频市场截帧图片
     * videoInterceptVideo：bmark-video-video、视频市场截帧视频
     * videoSampleFiless：bmark-video-source、视频市场样本文件
     */
    @ApiModelProperty(value="附件类型code", required = true)
    private String attachmentObjectTypeCode;

    @ApiModelProperty(value = "平台用户主键id")
    private String platformUserId;


    @ApiModelProperty(value="附件描述", required = true)
    private String fileDesc;

    @ApiModelProperty(value="是否进行加密", hidden = true)
    private Boolean isEncryption=true;

}