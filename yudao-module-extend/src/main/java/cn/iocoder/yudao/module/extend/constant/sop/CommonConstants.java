package cn.iocoder.yudao.module.extend.constant.sop;

/**
 * <AUTHOR>
 * 通用常量
 */
public interface CommonConstants {

	/**
	 * 商户平台版保中心注册默认用户id
	 */
	String PLATFORM_DEFAULT_USER_ID = "000";

	/***-----------------------------------------版权登记作品状态-------------------------------------------------*/
	/**初审中*/
	String FIRST_TRIAL = "1100";
	/**复审中*/
	String REEXAMINE = "1200";
	/**存证中*/
	String EVIDENCE = "1300";
	/**版权局审核中*/
	String COPYRIGHT_OFFICE_EXAMINE = "1400";
	/**登记完成*/
	String ENTRY_SUCCESS = "1000";
	/**初审驳回*/
	String FIRST_TRIAL_REJECT = "2100";
	/**复审驳回*/
	String REEXAMINE_REJECT = "2200";
	/**存证失败*/
	String EVIDENCE_FAIL = "2300";
	/**版权局审核驳回*/
	String COPYRIGHT_OFFICE_EXAMINE_REJECT = "2400";
	/***-----------------------------------------作品上传文件类型-------------------------------------------------*/
	/**权力保证书*/
	String FILE_TYPE_QLBZS = "authorityGuarantee";
	/**样本文件*/
	String FILE_TYPE_YBWJ = "sampleFiless";
	/**身份证明文件*/
	String FILE_TYPE_SFZMWJ = "identityDocument";
	/**其他材料*/
	String FILE_TYPE_QTCL = "otherMaterial";
	/**著作权人其他材料*/
	String FILE_TYPE_ZZQRQTCL = "ownerSampleFiless";

	/**存证证书*/
	String EVIDENCE_CERTIFICATE = "evidenceCertificate";

	/**登记证书*/
	String REGISTER_CERTIFICATE = "registerCertificate";
	/***-----------------------------------------著作权人状态-------------------------------------------------*/
	/**初审中*/
	String OWNER_TYPE = "1000";
	/**复审中*/
	String OWNER_DELETE = "1100";
	/**著作权人应用场景-登记*/
	String OWNER_SCENE_REGISTRATION = "registration";
	/**著作权人应用场景-存证*/
	String OWNER_DELETE_EVIDENCE = "evidence";

}
