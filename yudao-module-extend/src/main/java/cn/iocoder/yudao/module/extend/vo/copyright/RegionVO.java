package cn.iocoder.yudao.module.extend.vo.copyright;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 全国省市区
 *
 * <AUTHOR>
 * @date 2019-10-17 16:21:21
 */
@Data
@ApiModel
public class RegionVO {
private static final long serialVersionUID = 1L;

  /**
  * 省市区编码
  */
  @ApiModelProperty(hidden = true)
  private Long regionId;
  /**
  * 父级id
  */
  @ApiModelProperty(value = "父级id")
  private Long parRegionId;
  /**
  * 省市区名称
  */
  @ApiModelProperty(value = "省市区名称")
  private String regionName;
  /**
  * 层级
  */
  @ApiModelProperty(value = "层级")
  private Long regionLevel;
  /**
  * 类型
  */
  @ApiModelProperty(value = "类型")
  private Integer regionType;
  /**
  * 说明
  */
  @ApiModelProperty(value = "说明")
  private String regionDesc;
  /**
  * 区域排序
  */
  @ApiModelProperty(value = "区域排序")
  private Long regionSort;
  /**
  * 长途区号
  */
  @ApiModelProperty(value = "长途区号")
  private Long regionLong;
}
