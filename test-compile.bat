@echo off
echo 开始编译业务商品配置功能...

echo.
echo 1. 编译枚举类...
javac -cp "yudao-framework\yudao-spring-boot-starter-biz-data-permission\target\classes;yudao-framework\yudao-spring-boot-starter-biz-tenant\target\classes" yudao-module-business\src\main\java\cn\iocoder\yudao\module\business\enums\BusinessTypeEnum.java
if %errorlevel% neq 0 (
    echo 枚举类编译失败！
    pause
    exit /b 1
)

echo.
echo 2. 编译数据对象...
javac -cp "yudao-framework\yudao-spring-boot-starter-mybatis\target\classes" yudao-module-business\src\main\java\cn\iocoder\yudao\module\business\dal\dataobject\businesscfg\BusinessProductConfigDO.java
if %errorlevel% neq 0 (
    echo 数据对象编译失败！
    pause
    exit /b 1
)

echo.
echo 3. 编译完成！
echo 所有核心文件编译成功，可以继续开发。

echo.
echo 下一步建议：
echo 1. 执行数据库脚本：sql/mysql/business-product-config-2025-01-13.sql
echo 2. 配置相关权限
echo 3. 重启应用服务
echo 4. 测试API接口

pause
