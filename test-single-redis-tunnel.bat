@echo off
echo Testing Single SSH Tunnel
echo ==========================

set JUMP_HOST=**************
set JUMP_USER=tunneluser
set JUMP_PORT=2222

REM Redis production environment configuration
set REDIS_LOCAL_PORT=6380
set REDIS_REMOTE_HOST=************
set REDIS_REMOTE_PORT=6379

set PPK_KEY_PATH=..\2panel.jtstar.net.ppk

echo Testing database tunnel with key file...
echo Command: plink -ssh -L %REDIS_LOCAL_PORT%:%REDIS_REMOTE_HOST%:%REDIS_REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
echo.
echo This should connect successfully and then wait for Enter key.
echo After you see "Access granted. Press Return to begin session.", press Enter.
echo.
pause

echo Starting tunnel...
plink -ssh -L %REDIS_LOCAL_PORT%:%REDIS_REMOTE_HOST%:%REDIS_REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
