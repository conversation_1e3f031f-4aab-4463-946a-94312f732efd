import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

/**
 * SOP 诊断控制器
 * 用于排查 SOP 服务注入问题
 */
@RestController
@RequestMapping("/admin-api/business/sop-diagnosis")
public class SopDiagnosisController {

    @Autowired
    private ApplicationContext applicationContext;

    @GetMapping("/check")
    public Map<String, Object> checkSopStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 1. 检查 SOP 配置属性
            result.put("step1", "检查 SOP 配置属性");
            try {
                Object sopProperties = applicationContext.getBean("sopProperties");
                result.put("sopProperties", "存在: " + sopProperties.getClass().getName());
                
                // 使用反射获取配置值
                Class<?> clazz = sopProperties.getClass();
                Object enabled = clazz.getMethod("getEnabled").invoke(sopProperties);
                Object accessUrl = clazz.getMethod("getAccessUrl").invoke(sopProperties);
                Object appKey = clazz.getMethod("getAppKey").invoke(sopProperties);
                
                result.put("sopConfig", Map.of(
                    "enabled", enabled,
                    "accessUrl", accessUrl,
                    "appKey", appKey
                ));
            } catch (Exception e) {
                result.put("sopProperties", "不存在: " + e.getMessage());
            }
            
            // 2. 检查 SOP 服务 Bean
            result.put("step2", "检查 SOP 服务 Bean");
            try {
                Object sopService = applicationContext.getBean("sopServiceImpl");
                result.put("sopService", "存在: " + sopService.getClass().getName());
            } catch (Exception e) {
                result.put("sopService", "不存在: " + e.getMessage());
            }
            
            // 3. 检查 SOP 配置类
            result.put("step3", "检查 SOP 配置类");
            try {
                Object sopConfiguration = applicationContext.getBean("sopConfiguration");
                result.put("sopConfiguration", "存在: " + sopConfiguration.getClass().getName());
            } catch (Exception e) {
                result.put("sopConfiguration", "不存在: " + e.getMessage());
            }
            
            // 4. 检查所有 SOP 相关的 Bean
            result.put("step4", "检查所有 SOP 相关的 Bean");
            String[] beanNames = applicationContext.getBeanNamesForType(Object.class);
            Map<String, String> sopBeans = new HashMap<>();
            for (String beanName : beanNames) {
                if (beanName.toLowerCase().contains("sop")) {
                    Object bean = applicationContext.getBean(beanName);
                    sopBeans.put(beanName, bean.getClass().getName());
                }
            }
            result.put("sopBeans", sopBeans);
            
            // 5. 检查环境配置
            result.put("step5", "检查环境配置");
            try {
                String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
                result.put("activeProfiles", activeProfiles);
                
                String sopEnabled = applicationContext.getEnvironment().getProperty("extend.sop.enabled");
                String sopAccessUrl = applicationContext.getEnvironment().getProperty("extend.sop.access-url");
                String sopAppKey = applicationContext.getEnvironment().getProperty("extend.sop.app-key");
                
                result.put("environmentProperties", Map.of(
                    "extend.sop.enabled", sopEnabled != null ? sopEnabled : "null",
                    "extend.sop.access-url", sopAccessUrl != null ? sopAccessUrl : "null",
                    "extend.sop.app-key", sopAppKey != null ? sopAppKey : "null"
                ));
            } catch (Exception e) {
                result.put("environmentProperties", "获取失败: " + e.getMessage());
            }
            
            // 6. 检查条件注解
            result.put("step6", "检查条件注解");
            result.put("conditionInfo", Map.of(
                "expectedCondition", "@ConditionalOnProperty(prefix = \"extend.sop\", name = \"enabled\", havingValue = \"true\")",
                "description", "只有当 extend.sop.enabled=true 时，SOP 相关 Bean 才会被创建"
            ));
            
            result.put("status", "success");
            result.put("message", "诊断完成");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", "诊断过程中发生错误: " + e.getMessage());
            result.put("error", e.getClass().getName());
        }
        
        return result;
    }
    
    @GetMapping("/config-sources")
    public Map<String, Object> checkConfigSources() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 检查配置文件加载情况
            String[] activeProfiles = applicationContext.getEnvironment().getActiveProfiles();
            result.put("activeProfiles", activeProfiles);
            
            // 检查可能的配置文件
            String[] possibleConfigFiles = {
                "application.yaml",
                "application.yml", 
                "application-extend.yaml",
                "application-extend.yml",
                "application-dev.yaml",
                "application-prod.yaml",
                "application-preprod.yaml",
                "application-local.yaml"
            };
            
            result.put("possibleConfigFiles", possibleConfigFiles);
            result.put("note", "请检查当前激活的配置文件中是否包含 extend.sop.enabled=true");
            
        } catch (Exception e) {
            result.put("error", e.getMessage());
        }
        
        return result;
    }
}
