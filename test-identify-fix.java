import java.lang.reflect.Field;
import java.util.Arrays;

/**
 * 测试 IdentifyDO 字段修复
 * 验证是否还存在 statusDate、createStaff、createDate 字段
 */
public class TestIdentifyFix {
    
    public static void main(String[] args) {
        System.out.println("=== 测试 IdentifyDO 字段修复 ===");
        
        try {
            // 使用反射检查 IdentifyDO 类的字段
            Class<?> identifyDOClass = Class.forName("cn.iocoder.yudao.module.business.dal.dataobject.identify.IdentifyDO");
            Field[] fields = identifyDOClass.getDeclaredFields();
            
            System.out.println("IdentifyDO 类中的所有字段：");
            for (Field field : fields) {
                System.out.println("- " + field.getName() + " : " + field.getType().getSimpleName());
            }
            
            // 检查是否还存在问题字段
            boolean hasStatusDate = Arrays.stream(fields).anyMatch(f -> f.getName().equals("statusDate"));
            boolean hasCreateStaff = Arrays.stream(fields).anyMatch(f -> f.getName().equals("createStaff"));
            boolean hasCreateDate = Arrays.stream(fields).anyMatch(f -> f.getName().equals("createDate"));
            
            System.out.println("\n=== 字段检查结果 ===");
            System.out.println("statusDate 字段存在: " + hasStatusDate);
            System.out.println("createStaff 字段存在: " + hasCreateStaff);
            System.out.println("createDate 字段存在: " + hasCreateDate);
            
            if (!hasStatusDate && !hasCreateStaff && !hasCreateDate) {
                System.out.println("\n✅ 修复成功！所有问题字段都已移除。");
            } else {
                System.out.println("\n❌ 修复失败！仍然存在问题字段。");
            }
            
            // 检查是否保留了必要的字段
            boolean hasStatusCd = Arrays.stream(fields).anyMatch(f -> f.getName().equals("statusCd"));
            boolean hasRemark = Arrays.stream(fields).anyMatch(f -> f.getName().equals("remark"));
            boolean hasWcUserId = Arrays.stream(fields).anyMatch(f -> f.getName().equals("wcUserId"));
            
            System.out.println("\n=== 必要字段检查 ===");
            System.out.println("statusCd 字段存在: " + hasStatusCd);
            System.out.println("remark 字段存在: " + hasRemark);
            System.out.println("wcUserId 字段存在: " + hasWcUserId);
            
            if (hasStatusCd && hasRemark && hasWcUserId) {
                System.out.println("✅ 必要字段都已保留。");
            } else {
                System.out.println("❌ 缺少必要字段。");
            }
            
        } catch (ClassNotFoundException e) {
            System.out.println("❌ 无法找到 IdentifyDO 类: " + e.getMessage());
        }
    }
}
