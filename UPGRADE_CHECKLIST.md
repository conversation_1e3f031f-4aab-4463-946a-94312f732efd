# 🔍 升级验证清单

升级完成后，请按照此清单逐项验证系统功能，确保升级成功且业务功能正常。

## 📋 基础环境验证

### JDK 版本检查
- [ ] **JDK版本正确**
  ```bash
  java -version
  # 应显示目标JDK版本（8 或 21）
  ```

### Maven 配置检查
- [ ] **Maven编译成功**
  ```bash
  mvn clean compile
  # 应无错误完成编译
  ```

- [ ] **依赖解析正常**
  ```bash
  mvn dependency:tree | grep -i conflict
  # 应无依赖冲突
  ```

## 🚀 应用启动验证

### 启动检查
- [ ] **应用正常启动**
  ```bash
  mvn spring-boot:run
  # 应无错误启动，端口正常监听
  ```

- [ ] **启动日志正常**
  - [ ] 无ERROR级别日志
  - [ ] 数据库连接成功
  - [ ] Redis连接成功
  - [ ] 端口监听正常（默认48080）

### 健康检查
- [ ] **健康检查端点正常**
  ```bash
  curl http://localhost:48080/admin-api/actuator/health
  # 应返回 {"status":"UP"}
  ```

## 🔐 核心功能验证

### 用户认证模块
- [ ] **管理员登录**
  - 用户名：admin
  - 密码：admin123
  - 登录成功，获取token

- [ ] **用户权限验证**
  - [ ] 菜单权限正常显示
  - [ ] 按钮权限正常控制
  - [ ] 数据权限正常过滤

### 系统管理功能
- [ ] **用户管理**
  - [ ] 用户列表查询
  - [ ] 用户新增/编辑
  - [ ] 用户状态切换

- [ ] **角色管理**
  - [ ] 角色列表查询
  - [ ] 权限分配功能
  - [ ] 数据权限设置

- [ ] **菜单管理**
  - [ ] 菜单树显示
  - [ ] 菜单增删改查
  - [ ] 权限标识设置

- [ ] **部门管理**
  - [ ] 部门树显示
  - [ ] 部门增删改查
  - [ ] 数据权限关联

## 👥 业务模块验证

### 会员中心模块
- [ ] **会员注册功能**
  - [ ] 注册页面正常显示
  - [ ] 注册流程完整
  - [ ] 验证码功能正常
  - [ ] 数据库记录正确

- [ ] **会员管理功能**
  - [ ] 会员列表查询
  - [ ] 会员信息编辑
  - [ ] 会员状态管理

### AI聊天模块
- [ ] **AI对话功能**
  - [ ] 聊天界面正常
  - [ ] 消息发送接收
  - [ ] 对话历史记录
  - [ ] AI响应正常

### 外部接口模块
- [ ] **接口调用功能**
  - [ ] 外部API调用
  - [ ] 数据格式转换
  - [ ] 错误处理机制
  - [ ] 日志记录完整

### 业务模块
- [ ] **核心业务功能**
  - [ ] 业务流程完整
  - [ ] 数据处理正确
  - [ ] 业务规则生效
  - [ ] 异常处理正常

## 🔧 基础设施验证

### 数据库功能
- [ ] **数据库连接**
  - [ ] 主数据库连接正常
  - [ ] 读写分离配置（如有）
  - [ ] 事务处理正常

- [ ] **数据操作**
  - [ ] 增删改查功能
  - [ ] 分页查询正常
  - [ ] 数据完整性约束

### 缓存功能
- [ ] **Redis缓存**
  - [ ] Redis连接正常
  - [ ] 缓存读写功能
  - [ ] 缓存过期机制
  - [ ] 分布式锁功能

### 文件服务
- [ ] **文件上传**
  - [ ] 文件上传功能
  - [ ] 文件存储正常
  - [ ] 文件访问链接
  - [ ] 文件类型限制

### 消息队列
- [ ] **消息发送接收**
  - [ ] 消息生产正常
  - [ ] 消息消费正常
  - [ ] 死信队列处理
  - [ ] 消息持久化

## 📊 监控和日志

### 应用监控
- [ ] **性能监控**
  - [ ] CPU使用率正常
  - [ ] 内存使用正常
  - [ ] 响应时间合理
  - [ ] 吞吐量稳定

### 日志系统
- [ ] **日志记录**
  - [ ] 应用日志正常
  - [ ] 访问日志记录
  - [ ] 错误日志捕获
  - [ ] 日志级别控制

### 接口文档
- [ ] **Swagger文档**
  - [ ] 文档页面访问：http://localhost:48080/admin-api/doc.html
  - [ ] 接口列表完整
  - [ ] 接口测试功能
  - [ ] 参数说明准确

## 🧪 测试验证

### 单元测试
- [ ] **测试执行**
  ```bash
  mvn test
  # 所有测试应通过或已知失败
  ```

### 集成测试
- [ ] **端到端测试**
  - [ ] 完整业务流程测试
  - [ ] 多模块协作测试
  - [ ] 异常场景测试

## 🔄 兼容性验证

### 数据兼容性
- [ ] **历史数据**
  - [ ] 历史数据正常读取
  - [ ] 数据格式兼容
  - [ ] 数据迁移完整

### 接口兼容性
- [ ] **API接口**
  - [ ] 现有接口正常
  - [ ] 参数格式兼容
  - [ ] 返回结果一致

### 配置兼容性
- [ ] **配置文件**
  - [ ] 配置项正常加载
  - [ ] 环境变量生效
  - [ ] 外部配置兼容

## 📈 性能验证

### 响应时间
- [ ] **接口性能**
  - [ ] 登录接口 < 1s
  - [ ] 列表查询 < 2s
  - [ ] 复杂查询 < 5s
  - [ ] 文件上传合理

### 并发处理
- [ ] **并发测试**
  - [ ] 多用户同时访问
  - [ ] 高并发场景稳定
  - [ ] 资源竞争处理

## ✅ 验证完成确认

### 最终检查
- [ ] **所有功能验证通过**
- [ ] **性能指标达标**
- [ ] **无严重错误日志**
- [ ] **业务流程完整**

### 部署准备
- [ ] **测试环境验证完成**
- [ ] **生产环境配置准备**
- [ ] **回滚方案确认**
- [ ] **监控告警配置**

---

## 📝 验证记录

**验证人员**：_________________

**验证时间**：_________________

**验证环境**：_________________

**验证结果**：
- [ ] 全部通过，可以部署生产环境
- [ ] 部分问题，需要修复后重新验证
- [ ] 严重问题，需要回滚

**问题记录**：
```
记录发现的问题和解决方案
```

**备注说明**：
```
其他需要说明的事项
```
