// 简单的编译测试文件
// 用于验证关键类的导入是否正确

// 测试枚举类
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.enums.BusinessConfigStatusEnum;
import cn.iocoder.yudao.module.business.enums.BusinessOrderStatusEnum;

// 测试商品API
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;

public class TestKeyClasses {
    
    public static void main(String[] args) {
        System.out.println("测试枚举类...");
        
        // 测试业务类型枚举
        BusinessTypeEnum businessType = BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE;
        System.out.println("业务类型: " + businessType.getName());
        
        // 测试配置状态枚举
        BusinessConfigStatusEnum status = BusinessConfigStatusEnum.ACTIVE;
        System.out.println("配置状态: " + status.getName());
        
        // 测试订单状态枚举
        BusinessOrderStatusEnum orderStatus = BusinessOrderStatusEnum.PENDING;
        System.out.println("订单状态: " + orderStatus.getName());
        
        System.out.println("所有关键类导入成功！");
    }
}
