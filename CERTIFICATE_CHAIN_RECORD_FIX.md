# 数据资产存证上链记录查询问题修复

## 问题描述

在 `CertificateInfoAutoChainServiceImpl` 中，`isCertificateOnChain` 方法查询了 `data_certificate_chain_record` 表来判断是否已上链，但该表在实际数据库中不存在。实际的上链记录是存储在 `business_blockchain_link` 表中的。

## 问题分析

1. **表不存在问题**：
   - `data_certificate_chain_record` 表在数据库中不存在，导致查询失败
   - 实际的上链记录存储在 `business_blockchain_link` 表中

2. **代码逻辑问题**：
   - `Copyright2Sop.issueNft` 方法会在 `business_blockchain_link` 表中创建记录
   - 但 `isCertificateOnChain` 方法查询的是不存在的 `data_certificate_chain_record` 表
   - `issueNftForCertificate` 方法也试图操作不存在的表

## 修复方案

完全移除对不存在的 `data_certificate_chain_record` 表的依赖，改为只查询实际存在的 `business_blockchain_link` 表：

1. **移除 `CertificateChainRecordMapper` 依赖**：不再使用不存在的表
2. **简化 `isCertificateOnChain` 方法**：只查询 `business_blockchain_link` 表
3. **简化 `issueNftForCertificate` 方法**：移除对不存在表的操作，直接调用NFT发行服务

## 修改内容

### 1. 添加依赖注入

```java
@Resource
private BusinessBlockchainLinkService businessBlockchainLinkService;
```

### 2. 修改查询逻辑

```java
@Override
public boolean isCertificateOnChain(Integer certificateInfoId) {
    // 查询 business_blockchain_link 表中的上链记录
    // 这是实际的上链记录存储位置
    var blockchainLinks = businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
            certificateInfoId.longValue(),
            BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
    );

    boolean isOnChain = !blockchainLinks.isEmpty();

    log.debug("检查数据资产是否已上链，存证ID: {}, business_blockchain_link记录数量: {}, 结果: {}",
            certificateInfoId, blockchainLinks.size(), isOnChain);

    return isOnChain;
}
```

### 3. 简化NFT发行逻辑

```java
try {
    // 调用NFT发行服务
    Map<String, String> nftResult = businessNftIssueService.issueNftForBusiness(
            BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE,
            certificateInfoId.longValue(),
            address
    );

    // 返回结果摘要
    String blockchainId = nftResult.get("blockchainId");
    String transactionHash = nftResult.get("transactionHash");
    return String.format("NFT发行成功 - 区块链ID: %s, 交易哈希: %s", blockchainId, transactionHash);

} catch (Exception e) {
    log.error("数据资产NFT发行失败，存证ID: {}, 错误: {}", certificateInfoId, e.getMessage(), e);
    throw e;
}
```

## 测试验证

创建了完整的单元测试 `CertificateInfoAutoChainServiceImplTest`，覆盖以下场景：

1. **有区块链记录**：应该返回 true
2. **没有任何记录**：应该返回 false
3. **多个区块链记录**：应该返回 true

## 影响范围

- **移除依赖**：完全移除了对不存在的 `data_certificate_chain_record` 表的依赖
- **简化逻辑**：代码更加简洁，只查询实际存在的表
- **性能提升**：减少了对不存在表的查询尝试，避免了数据库错误

## 相关文件

- `yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/service/certificateinfo/impl/CertificateInfoAutoChainServiceImpl.java`
- `yudao-module-business/src/test/java/cn/iocoder/yudao/module/business/service/certificateinfo/CertificateInfoAutoChainServiceImplTest.java`

## 建议

1. **清理无用代码**：可以考虑删除 `CertificateChainRecordDO` 和 `CertificateChainRecordMapper` 相关代码
2. **数据库清理**：如果确认不需要 `data_certificate_chain_record` 表，可以删除相关的SQL脚本
3. **统一存储策略**：确保所有上链记录都统一存储在 `business_blockchain_link` 表中
