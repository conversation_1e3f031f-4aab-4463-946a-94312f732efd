# 身份区块链链接SOP集成说明

## 概述

身份区块链链接功能已完全集成SOP（Secure Operation Platform）区块链服务，实现自动获取区块链钱包地址、私钥、公钥等信息。

## 集成特性

### 1. 自动注册机制
- **智能判断**：创建区块链链接时，如果缺少区块链信息，自动触发SOP注册
- **无缝集成**：SOP注册过程对用户透明，自动完成
- **失败容错**：SOP注册失败不会阻断创建流程，确保系统稳定性

### 2. 手动注册支持
- **手动触发**：提供API接口手动触发SOP注册
- **强制重注册**：支持强制重新注册，覆盖现有区块链信息
- **状态管理**：自动更新注册状态和结果

### 3. 数据解析和存储
- **响应解析**：自动解析SOP API响应，提取关键信息
- **数据存储**：将区块链地址、私钥、公钥等信息存储到数据库
- **状态同步**：根据注册结果自动更新状态

## 技术实现

### 1. 触发条件
系统会在以下情况下自动触发SOP注册：
```java
private boolean needSopRegister(IdentityBlockchainLinkSaveReqVO createReqVO) {
    return (createReqVO.getAddress() == null || createReqVO.getAddress().trim().isEmpty()) ||
           (createReqVO.getPrivateKey() == null || createReqVO.getPrivateKey().trim().isEmpty()) ||
           (createReqVO.getPublicKey() == null || createReqVO.getPublicKey().trim().isEmpty());
}
```

### 2. SOP请求构建
```java
private BlockChainUserDto buildBlockChainUserDto(IdentityBlockchainLinkDO identityBlockchainLink, IdentifyDO identifyDO) {
    BlockChainUserDto dto = new BlockChainUserDto();
    dto.setPlatformUserId(generatePlatformUserId(identifyDO));
    dto.setUserName(identifyDO.getIdName());
    dto.setUserType(convertUserTypeForSop(userType));
    dto.setPhone(identifyDO.getPhone());
    dto.setEmail(identifyDO.getEmail());
    dto.setRemark("通过文创链平台自动注册");
    return dto;
}
```

### 3. 响应解析
```java
private void parseSopResponseAndUpdateLink(IdentityBlockchainLinkDO identityBlockchainLink, JSONObject sopResponse) {
    if (sopResponse.containsKey("success") && sopResponse.getBool("success")) {
        JSONObject data = sopResponse.getJSONObject("data");
        if (data != null) {
            identityBlockchainLink.setAddress(data.getStr("address"));
            identityBlockchainLink.setPrivateKey(data.getStr("privateKey"));
            identityBlockchainLink.setPublicKey(data.getStr("publicKey"));
            identityBlockchainLink.setStatusCd("A"); // 激活状态
        }
    }
}
```

## API接口

### 1. 自动创建（含SOP注册）
```http
POST /business/identity-blockchain-link/create
Content-Type: application/json

{
  "identifyId": 1001,
  "realName": "张三",
  "userType": "0",
  "platformId": "wenchuang",
  "remark": "自动创建并注册到区块链"
}
```

**成功响应（SOP注册成功）：**
```json
{
  "code": 0,
  "data": 100,
  "msg": "操作成功"
}
```

### 2. 根据实名认证自动创建
```http
POST /business/identity-blockchain-link/create-by-identify?identifyId=1001
```

### 3. 手动触发SOP注册
```http
POST /business/identity-blockchain-link/register-to-sop?blockchainId=100&forceRegister=false
```

**成功响应：**
```json
{
  "code": 0,
  "data": true,
  "msg": "操作成功"
}
```

**失败响应：**
```json
{
  "code": 0,
  "data": false,
  "msg": "操作成功"
}
```

## 配置要求

### 1. SOP服务配置
确保在`application.yml`中正确配置SOP服务：

```yaml
extend:
  sop:
    enabled: true
    access-url: https://openapi.bmark.cn/
    app-key: your-app-key
    private-key: your-private-key
    public-key: your-public-key
    platform-public-key: platform-public-key
    api:
      blockchain-register: blockchain.register
```

### 2. 依赖检查
系统会自动检查SOP服务是否可用：
```java
@Resource(required = false)
private SopService sopService;

if (sopService == null) {
    log.warn("SOP服务未启用，跳过区块链注册");
    return;
}
```

## 使用场景

### 场景1：用户完成实名认证后自动创建区块链钱包（已优化）
```java
@Transactional
public void approveIdentify(Long identifyId) {
    // 1. 更新实名认证状态
    identifyService.updateStatus(identifyId, "APPROVED");

    // 2. 自动创建区块链链接（含SOP注册，但不自动发行NFT）
    Long linkId = identityBlockchainLinkService.createIdentityBlockchainLinkByIdentify(identifyId);
    log.info("自动创建区块链链接并注册成功，linkId: {}，NFT发行已暂停", linkId);

    // 3. 如需发行NFT，可手动触发
    // identityBlockchainLinkService.issueNftForIdentity(linkId);
}
```

### 场景2：批量用户区块链钱包创建
```java
@Transactional
public void batchCreateBlockchainWallets(List<Long> identifyIds) {
    for (Long identifyId : identifyIds) {
        try {
            identityBlockchainLinkService.createIdentityBlockchainLinkByIdentify(identifyId);
            log.info("用户[{}]区块链钱包创建成功", identifyId);
        } catch (Exception e) {
            log.error("用户[{}]区块链钱包创建失败", identifyId, e);
        }
    }
}
```

### 场景3：重试失败的SOP注册
```java
@Scheduled(fixedDelay = 300000) // 每5分钟执行一次
public void retryFailedSopRegistrations() {
    // 查询状态为未激活的记录
    List<IdentityBlockchainLinkDO> failedLinks = identityBlockchainLinkMapper.selectByStatus("I");

    for (IdentityBlockchainLinkDO link : failedLinks) {
        if (shouldRetry(link)) {
            identityBlockchainLinkService.registerToSopBlockchain(link.getBlockchainId(), false);
        }
    }
}
```

### 场景4：基于业务类型的NFT发行架构

**业务类型与NFT发行支持**
| 业务类型 | 枚举值 | NFT发行支持 | 类别ID | 状态 |
|---------|--------|------------|--------|------|
| 数据知识产权存证 | `DATA_CERTIFICATE_EVIDENCE` | ✅ 支持 | 54 | 已实现 |
| 软件著作权登记 | `SOFTWARE_REGISTER` | ✅ 支持 | 55 | 预留 |
| 实名认证 | `REAL_IDENTIFY` | ❌ 不支持 | - | 不适用 |
| 作品著作权登记 | `COPYRIGHT_REGISTER` | ✅ 支持 | 56 | 预留 |
| 区块链存证 | `BLOCKCHAIN_EVIDENCE` | ✅ 支持 | 57 | 预留 |

**NFT发行数据转换架构**
```java
// 1. 业务数据转换器接口
public interface BusinessNftDataConverter {
    boolean supports(BusinessTypeEnum businessType);
    WCDACDto convertToWCDACDto(Long businessId, String address);
    Long getCategoryId(BusinessTypeEnum businessType);
}

// 2. 统一NFT发行服务
@Service
public class BusinessNftIssueServiceImpl {
    public Map<String, String> issueNftForBusiness(
        BusinessTypeEnum businessType, Long businessId, String address);
}

// 3. 管理后台统一接口
@PostMapping("/business/nft/issue")
public CommonResult<Map<String, String>> issueNft(
    String businessTypeCode, Long businessId, String address);

// 4. 用户 App统一接口
@PostMapping("/app-api/business/nft/issue")
public CommonResult<Map<String, String>> issueNft(
    String businessTypeCode, Long businessId, String address);
```

### 场景5：批量NFT发行
```java
@Transactional
public void batchIssueNft(List<Long> blockchainIds) {
    for (Long blockchainId : blockchainIds) {
        try {
            identityBlockchainLinkService.issueNftForIdentity(blockchainId);
            log.info("NFT发行成功，区块链链接ID: {}", blockchainId);
        } catch (Exception e) {
            log.error("NFT发行失败，区块链链接ID: {}", blockchainId, e);
        }
    }
}
```

## 状态管理

### 状态说明
- **I（未激活）**：初始状态或SOP注册失败
- **A（激活）**：SOP注册成功，已获取区块链信息
- **N（NFT已发行）**：完成NFT发行（仅适用于支持NFT发行的业务类型）
- **S（暂停）**：手动暂停使用
- **D（禁用）**：手动禁用

### 业务类型与状态流转

#### 实名认证业务
- 仅进行SOP用户注册，获取区块链地址
- 状态流转：`I`（初始）→ `A`（SOP注册成功）
- 不支持NFT发行，不会有 `N` 状态

#### 其他业务（数据存证、软著登记等）
- 支持完整的SOP注册和NFT发行流程
- 状态流转：`I`（初始）→ `A`（SOP注册成功）→ `N`（NFT已发行）
- 通过统一的业务NFT发行服务进行NFT发行

### 状态转换
```
创建 -> I（未激活）
  |
  v
SOP注册成功 -> A（激活）
  |
  v
SOP注册失败 -> I（未激活，记录失败原因）
```

## 错误处理

### 1. SOP服务不可用
```java
if (sopService == null) {
    log.warn("SOP服务未启用，跳过区块链注册");
    // 继续创建流程，但不进行区块链注册
    return;
}
```

### 2. SOP注册失败
```java
catch (Exception e) {
    log.error("SOP区块链注册失败，实名认证ID: {}", identifyId, e);
    // 记录错误信息但不阻断创建流程
    identityBlockchainLink.setRemark("SOP区块链注册失败: " + e.getMessage());
    identityBlockchainLink.setStatusCd("I"); // 设置为未激活状态
}
```

### 3. 响应解析失败
```java
catch (Exception e) {
    log.error("解析SOP响应失败", e);
    identityBlockchainLink.setStatusCd("I");
    identityBlockchainLink.setRemark("解析SOP响应失败: " + e.getMessage());
}
```

## 监控和维护

### 1. 日志监控
- 监控SOP注册成功率
- 监控SOP响应时间
- 监控失败原因分布

### 2. 数据统计
```sql
-- 注册成功率统计
SELECT 
    statusCd,
    COUNT(*) as count,
    COUNT(*) * 100.0 / (SELECT COUNT(*) FROM identity_blockchain_link) as percentage
FROM identity_blockchain_link 
GROUP BY statusCd;

-- 失败原因统计
SELECT 
    LEFT(remark, 50) as error_type,
    COUNT(*) as count
FROM identity_blockchain_link 
WHERE statusCd = 'I' AND remark LIKE '%失败%'
GROUP BY LEFT(remark, 50)
ORDER BY count DESC;
```

### 3. 定期维护
- 定期重试失败的注册
- 清理过期的错误记录
- 监控SOP服务健康状态

## 安全考虑

1. **私钥安全**：从SOP获取的私钥需要加密存储
2. **传输安全**：与SOP通信使用HTTPS和签名验证
3. **访问控制**：严格控制区块链信息的访问权限
4. **日志脱敏**：在日志中对敏感信息进行脱敏处理

## 扩展功能

### 1. 异步注册
可以考虑将SOP注册改为异步处理，提高响应速度：
```java
@Async
public CompletableFuture<Void> registerToBlockchainAsync(Long blockchainId) {
    // 异步执行SOP注册
}
```

### 2. 重试机制
实现智能重试机制，对临时失败进行自动重试：
```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public JSONObject blockchainRegisterWithRetry(BlockChainUserDto dto) {
    return sopService.blockchainRegister(dto);
}
```

### 3. 缓存优化
对频繁查询的区块链信息进行缓存：
```java
@Cacheable(value = "blockchain-links", key = "#identifyId")
public IdentityBlockchainLinkDO getByIdentifyIdWithCache(Long identifyId) {
    return identityBlockchainLinkMapper.selectValidByIdentifyId(identifyId);
}
```

## NFT发行接口说明

### 管理后台接口（仅限admin权限）
- **路径**: `/business/nft/issue`
- **权限**: `@PreAuthorize("@ss.hasPermission('business:nft:issue')")`
- **用途**: 管理员为任意业务类型发行NFT

### 用户 App接口（用户登录即可）
- **路径**: `/app-api/business/nft/issue`
- **权限**: `@PreAuthenticated`（仅需登录）
- **用途**: 用户为自己的业务数据发行NFT

### 实名认证专用接口（会抛出异常）
- **Admin路径**: `/admin-api/business/identity-blockchain-link/issue-nft`
- **App路径**: `/app-api/business/identity-blockchain-link/issue-nft`
- **说明**: 由于实名认证不支持NFT发行，调用这些接口会抛出异常

## 扩展新业务类型

### 步骤
1. 在 `BusinessTypeEnum` 中添加新的业务类型
2. 实现对应的 `BusinessNftDataConverter` 接口
3. 无需修改核心NFT发行逻辑，自动支持新业务类型

### 示例：添加新业务类型
```java
// 1. 在BusinessTypeEnum中添加
NEW_BUSINESS("NEW_BUSINESS", "新业务类型"),

// 2. 实现转换器
@Component
public class NewBusinessNftDataConverter implements BusinessNftDataConverter {
    @Override
    public boolean supports(BusinessTypeEnum businessType) {
        return BusinessTypeEnum.NEW_BUSINESS.equals(businessType);
    }

    @Override
    public WCDACDto convertToWCDACDto(Long businessId, String address) {
        // 实现具体的数据转换逻辑
    }

    @Override
    public Long getCategoryId(BusinessTypeEnum businessType) {
        return 58L; // 新的类别ID
    }
}
```

## 数据资产审核通过后自动上链

### 功能概述
当数据资产存证审核状态变更为"审核通过"（状态码：C）时，系统会自动触发NFT发行上链流程。

### 核心组件

#### 1. 审核状态枚举
```java
public enum CertificateReviewStatusEnum {
    PENDING("P", "待审核"),
    APPROVED("C", "审核通过"),  // 触发自动上链
    REJECTED("R", "审核不通过"),
    DRAFT("D", "草稿");
}
```

#### 2. 自动上链服务
```java
@Service
public class CertificateInfoAutoChainServiceImpl {
    @Async
    public void handleCertificateReviewStatusChange(Integer certificateInfoId, String reviewStatus);

    public String issueNftForCertificate(Integer certificateInfoId);

    public boolean isCertificateOnChain(Integer certificateInfoId);
}
```

#### 3. 上链记录管理
- **表名**: `data_certificate_chain_record`
- **状态**: I-初始，P-发行中，S-发行成功，F-发行失败
- **记录**: 区块链ID、交易哈希、发行时间、失败原因等

### 自动上链流程

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant Review as 审核服务
    participant Chain as 自动上链服务
    participant NFT as NFT发行服务
    participant BC as 区块链

    Admin->>Review: 更新审核状态为"审核通过"
    Review->>Review: 检查状态变化
    Review->>Chain: 异步触发自动上链
    Chain->>Chain: 检查是否已上链
    Chain->>Chain: 创建上链记录(发行中)
    Chain->>NFT: 调用NFT发行服务
    NFT->>BC: 发行NFT到区块链
    BC-->>NFT: 返回交易结果
    NFT-->>Chain: 返回发行结果
    Chain->>Chain: 更新上链记录(成功/失败)
```

### 关键特性

1. **异步处理**: 使用 `@Async` 注解，不影响审核操作响应时间
2. **重复检查**: 自动检查是否已上链，避免重复发行
3. **状态管理**: 完整的上链状态跟踪和记录
4. **错误处理**: 失败时记录详细错误信息，支持后续重试
5. **事务安全**: 使用事务确保数据一致性

### 使用示例

```java
// 审核通过后自动触发
certificateReviewService.updateCertificateReview(updateReqVO);
// 系统会自动检测状态变化并触发上链

// 手动检查上链状态
boolean isOnChain = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

// 手动触发上链（如果需要）
String result = certificateInfoAutoChainService.issueNftForCertificate(certificateInfoId);
```

这个SOP集成方案提供了完整的区块链钱包自动创建和管理功能，以及可扩展的NFT发行架构，包括数据资产审核通过后的自动上链机制，确保了系统的稳定性和用户体验。
