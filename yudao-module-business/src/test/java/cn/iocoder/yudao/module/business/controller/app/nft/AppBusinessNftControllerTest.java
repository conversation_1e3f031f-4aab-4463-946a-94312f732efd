package cn.iocoder.yudao.module.business.controller.app.nft;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftDataConverterFactory;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftIssueService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 用户 App业务NFT发行控制器测试
 *
 * <AUTHOR>
 */
public class AppBusinessNftControllerTest extends BaseDbUnitTest {

    @InjectMocks
    private AppBusinessNftController appBusinessNftController;

    @Mock
    private BusinessNftIssueService businessNftIssueService;

    @Mock
    private BusinessNftDataConverterFactory converterFactory;

    @Test
    public void testIssueNft_Success() {
        // 准备测试数据
        String businessTypeCode = "data_certificate_evidence";
        Long businessId = 1L;
        String address = "0x1234567890abcdef";

        // 模拟NFT发行成功的响应
        Map<String, String> expectedResult = new HashMap<>();
        expectedResult.put("blockchainId", "12345");
        expectedResult.put("transactionHash", "0xabcdef123456");

        when(businessNftIssueService.issueNftForBusiness(
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE), eq(businessId), eq(address)))
                .thenReturn(expectedResult);

        // 模拟用户登录状态
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

            // 执行测试
            CommonResult<Map<String, String>> result = appBusinessNftController.issueNft(
                    businessTypeCode, businessId, address);

            // 验证结果
            assertTrue(result.isSuccess());
            assertEquals(expectedResult, result.getData());
            verify(businessNftIssueService, times(1))
                    .issueNftForBusiness(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE, businessId, address);
        }
    }

    @Test
    public void testIssueNft_UnsupportedBusinessType() {
        // 准备测试数据
        String businessTypeCode = "INVALID_TYPE";
        Long businessId = 1L;
        String address = "0x1234567890abcdef";

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> {
            appBusinessNftController.issueNft(businessTypeCode, businessId, address);
        });

        // 验证不会调用NFT发行服务
        verify(businessNftIssueService, never())
                .issueNftForBusiness(any(), any(), any());
    }

    @Test
    public void testIssueNft_RealIdentifyNotSupported() {
        // 准备测试数据
        String businessTypeCode = "real_identify";
        Long businessId = 1L;
        String address = "0x1234567890abcdef";

        // 模拟实名认证不支持NFT发行的异常
        when(businessNftIssueService.issueNftForBusiness(
                eq(BusinessTypeEnum.REAL_IDENTIFY), eq(businessId), eq(address)))
                .thenThrow(new IllegalArgumentException("业务类型 实名认证 不支持NFT发行"));

        // 模拟用户登录状态
        try (MockedStatic<SecurityFrameworkUtils> mockedStatic = mockStatic(SecurityFrameworkUtils.class)) {
            mockedStatic.when(SecurityFrameworkUtils::getLoginUserId).thenReturn(1L);

            // 执行测试并验证异常
            assertThrows(IllegalArgumentException.class, () -> {
                appBusinessNftController.issueNft(businessTypeCode, businessId, address);
            });
        }
    }

    @Test
    public void testGetSupportedBusinessTypes() {
        // 模拟业务类型支持情况
        when(businessNftIssueService.isNftSupported(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE))
                .thenReturn(true);
        when(businessNftIssueService.isNftSupported(BusinessTypeEnum.REAL_IDENTIFY))
                .thenReturn(false);
        when(businessNftIssueService.isNftSupported(BusinessTypeEnum.SOFTWARE_REGISTER))
                .thenReturn(true);

        // 模拟类别ID获取
        when(converterFactory.getCategoryId(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE))
                .thenReturn(54L);
        when(converterFactory.getCategoryId(BusinessTypeEnum.SOFTWARE_REGISTER))
                .thenReturn(55L);

        // 执行测试
        CommonResult<List<AppBusinessNftController.BusinessTypeInfo>> result = 
                appBusinessNftController.getSupportedBusinessTypes();

        // 验证结果
        assertTrue(result.isSuccess());
        assertNotNull(result.getData());
        
        // 验证数据资产存证支持NFT发行
        AppBusinessNftController.BusinessTypeInfo dataEvidence = result.getData().stream()
                .filter(info -> "data_certificate_evidence".equals(info.getCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(dataEvidence);
        assertTrue(dataEvidence.isNftSupported());
        assertEquals(54L, dataEvidence.getCategoryId());

        // 验证实名认证不支持NFT发行
        AppBusinessNftController.BusinessTypeInfo realIdentify = result.getData().stream()
                .filter(info -> "real_identify".equals(info.getCode()))
                .findFirst()
                .orElse(null);
        assertNotNull(realIdentify);
        assertFalse(realIdentify.isNftSupported());
        assertNull(realIdentify.getCategoryId());
    }

    @Test
    public void testCheckNftSupport_Supported() {
        // 准备测试数据
        String businessTypeCode = "data_certificate_evidence";

        when(businessNftIssueService.isNftSupported(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE))
                .thenReturn(true);

        // 执行测试
        CommonResult<Boolean> result = appBusinessNftController.checkNftSupport(businessTypeCode);

        // 验证结果
        assertTrue(result.isSuccess());
        assertTrue(result.getData());
    }

    @Test
    public void testCheckNftSupport_NotSupported() {
        // 准备测试数据
        String businessTypeCode = "real_identify";

        when(businessNftIssueService.isNftSupported(BusinessTypeEnum.REAL_IDENTIFY))
                .thenReturn(false);

        // 执行测试
        CommonResult<Boolean> result = appBusinessNftController.checkNftSupport(businessTypeCode);

        // 验证结果
        assertTrue(result.isSuccess());
        assertFalse(result.getData());
    }

    @Test
    public void testCheckNftSupport_InvalidBusinessType() {
        // 准备测试数据
        String businessTypeCode = "INVALID_TYPE";

        // 执行测试
        CommonResult<Boolean> result = appBusinessNftController.checkNftSupport(businessTypeCode);

        // 验证结果
        assertTrue(result.isSuccess());
        assertFalse(result.getData());
    }
}
