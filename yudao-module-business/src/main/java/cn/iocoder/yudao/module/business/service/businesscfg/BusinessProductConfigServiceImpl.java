package cn.iocoder.yudao.module.business.service.businesscfg;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigCreateReqVO;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigPageReqVO;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigUpdateReqVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigRespVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigDetailRespVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessTypeRespVO;
import cn.iocoder.yudao.module.business.convert.businesscfg.BusinessProductConfigConvert;
import cn.iocoder.yudao.module.business.dal.dataobject.businesscfg.BusinessProductConfigDO;
import cn.iocoder.yudao.module.business.dal.mysql.businesscfg.BusinessProductConfigMapper;
import cn.iocoder.yudao.module.business.enums.BusinessConfigStatusEnum;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.product.api.spu.ProductSpuApi;
import cn.iocoder.yudao.module.product.api.spu.dto.ProductSpuRespDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.business.enums.ErrorCodeConstants.*;

/**
 * 业务商品配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class BusinessProductConfigServiceImpl implements BusinessProductConfigService {

    @Resource
    private BusinessProductConfigMapper businessProductConfigMapper;

    @Resource
    private ProductSpuApi productSpuApi;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createBusinessProductConfig(BusinessProductConfigCreateReqVO createReqVO) {
        // 校验业务类型
        validateBusinessType(createReqVO.getBusinessType());
        // 校验商品SPU
        validateProductSpu(createReqVO.getProductSpuId());
        // 校验业务类型和商品SPU的唯一性
        validateBusinessProductUnique(createReqVO.getBusinessType(), createReqVO.getProductSpuId(), null);
        
        // 插入
        BusinessProductConfigDO config = BusinessProductConfigConvert.INSTANCE.convert(createReqVO);
        
        // 处理默认配置逻辑
        handleDefaultConfig(config);
        
        businessProductConfigMapper.insert(config);
        // 返回
        return config.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBusinessProductConfig(BusinessProductConfigUpdateReqVO updateReqVO) {
        // 校验存在
        BusinessProductConfigDO existingConfig = validateBusinessProductConfigExists(updateReqVO.getId());
        // 校验业务类型
        validateBusinessType(updateReqVO.getBusinessType());
        // 校验商品SPU
        validateProductSpu(updateReqVO.getProductSpuId());
        // 校验业务类型和商品SPU的唯一性
        validateBusinessProductUnique(updateReqVO.getBusinessType(), updateReqVO.getProductSpuId(), updateReqVO.getId());
        
        // 更新
        BusinessProductConfigDO updateObj = BusinessProductConfigConvert.INSTANCE.convert(updateReqVO);
        
        // 处理默认配置逻辑
        if (Boolean.TRUE.equals(updateReqVO.getIsDefault()) && !Boolean.TRUE.equals(existingConfig.getIsDefault())) {
            handleDefaultConfig(updateObj);
        }
        
        businessProductConfigMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBusinessProductConfig(Long id) {
        // 校验存在
        BusinessProductConfigDO config = validateBusinessProductConfigExists(id);
        
        // 如果是默认配置，检查是否还有其他配置
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            List<BusinessProductConfigDO> otherConfigs = businessProductConfigMapper.selectListByBusinessType(config.getBusinessType());
            otherConfigs.removeIf(c -> c.getId().equals(id));
            if (CollUtil.isEmpty(otherConfigs)) {
                throw exception(BUSINESS_PRODUCT_CONFIG_DEFAULT_DELETE);
            }
            // 设置第一个其他配置为默认
            BusinessProductConfigDO newDefault = otherConfigs.get(0);
            newDefault.setIsDefault(true);
            businessProductConfigMapper.updateById(newDefault);
        }
        
        // 删除
        businessProductConfigMapper.deleteById(id);
    }

    @Override
    public BusinessProductConfigDO getBusinessProductConfig(Long id) {
        return businessProductConfigMapper.selectById(id);
    }

    @Override
    public PageResult<BusinessProductConfigDO> getBusinessProductConfigPage(BusinessProductConfigPageReqVO pageReqVO) {
        return businessProductConfigMapper.selectPage(pageReqVO);
    }

    @Override
    public List<BusinessProductConfigDO> getBusinessProductConfigList(BusinessProductConfigPageReqVO exportReqVO) {
        return businessProductConfigMapper.selectPage(exportReqVO).getList();
    }

    @Override
    public List<BusinessProductConfigDO> getBusinessProductConfigListByType(String businessType) {
        return businessProductConfigMapper.selectListByBusinessType(businessType);
    }

    @Override
    public List<BusinessProductConfigDO> getBusinessProductConfigListByTypeAndStatus(String businessType, String status) {
        return businessProductConfigMapper.selectListByBusinessTypeAndStatus(businessType, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefaultConfig(Long id) {
        // 校验存在
        BusinessProductConfigDO config = validateBusinessProductConfigExists(id);
        
        // 如果已经是默认配置，直接返回
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            return;
        }
        
        // 取消同业务类型的其他默认配置
        BusinessProductConfigDO currentDefault = businessProductConfigMapper.selectDefaultByBusinessType(config.getBusinessType());
        if (currentDefault != null) {
            currentDefault.setIsDefault(false);
            businessProductConfigMapper.updateById(currentDefault);
        }
        
        // 设置当前配置为默认
        config.setIsDefault(true);
        businessProductConfigMapper.updateById(config);
    }

    @Override
    public BusinessProductConfigDO getDefaultConfig(String businessType) {
        return businessProductConfigMapper.selectDefaultByBusinessType(businessType);
    }

    @Override
    public BusinessProductConfigDO validateBusinessProductConfigExists(Long id) {
        BusinessProductConfigDO config = businessProductConfigMapper.selectById(id);
        if (config == null) {
            throw exception(BUSINESS_PRODUCT_CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @Override
    public List<BusinessProductConfigDO> validateSpuList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        List<BusinessProductConfigDO> configs = businessProductConfigMapper.selectListByProductSpuIds(new ArrayList<>(ids));
        Map<Long, BusinessProductConfigDO> configMap = configs.stream()
                .collect(Collectors.toMap(BusinessProductConfigDO::getProductSpuId, c -> c));
        
        // 校验所有SPU都有对应的配置
        for (Long spuId : ids) {
            if (!configMap.containsKey(spuId)) {
                throw exception(BUSINESS_PRODUCT_CONFIG_NOT_EXISTS);
            }
        }
        return configs;
    }

    // ==================== 用户 App接口实现 ====================

    @Override
    public AppBusinessConfigRespVO getAppBusinessConfigs(String businessType) {
        // 校验业务类型
        validateBusinessType(businessType);
        
        // 查询该业务类型的所有激活配置
        List<BusinessProductConfigDO> configs = getBusinessProductConfigListByTypeAndStatus(
                businessType, BusinessConfigStatusEnum.ACTIVE.getStatus());
        
        if (CollUtil.isEmpty(configs)) {
            return createEmptyAppBusinessConfigRespVO(businessType);
        }
        
        // 按默认配置和排序字段排序
        configs.sort((a, b) -> {
            if (Boolean.TRUE.equals(a.getIsDefault()) && !Boolean.TRUE.equals(b.getIsDefault())) return -1;
            if (!Boolean.TRUE.equals(a.getIsDefault()) && Boolean.TRUE.equals(b.getIsDefault())) return 1;
            return a.getSortOrder().compareTo(b.getSortOrder());
        });
        
        // 转换为用户 AppVO
        AppBusinessConfigRespVO respVO = new AppBusinessConfigRespVO();
        respVO.setBusinessType(businessType);
        respVO.setBusinessName(BusinessProductConfigConvert.INSTANCE.getBusinessTypeName(businessType));
        
        List<AppBusinessConfigRespVO.ConfigItem> configItems = BusinessProductConfigConvert.INSTANCE.convertToAppConfigItemList(configs);
        
        // TODO: 集成商品价格信息
        // 这里需要调用商品模块API获取价格信息
        enrichPriceInfo(configItems);
        
        respVO.setConfigs(configItems);
        return respVO;
    }

    @Override
    public AppBusinessConfigDetailRespVO getAppBusinessConfigDetail(Long configId) {
        // 校验存在
        BusinessProductConfigDO config = validateBusinessProductConfigExists(configId);
        
        // 转换为用户 App详情VO
        AppBusinessConfigDetailRespVO respVO = BusinessProductConfigConvert.INSTANCE.convertToAppDetail(config);
        
        // TODO: 集成商品价格信息
        // 这里需要调用商品模块API获取价格信息
        enrichDetailPriceInfo(respVO);
        
        return respVO;
    }

    @Override
    public List<AppBusinessTypeRespVO> getAppBusinessTypes() {
        List<AppBusinessTypeRespVO> result = new ArrayList<>();
        for (BusinessTypeEnum typeEnum : BusinessTypeEnum.values()) {
            AppBusinessTypeRespVO vo = new AppBusinessTypeRespVO();
            vo.setCode(typeEnum.getCode());
            vo.setName(typeEnum.getName());
            vo.setDescription(getBusinessTypeDescription(typeEnum));
            result.add(vo);
        }
        return result;
    }

    // ==================== 私有方法 ====================

    private void validateBusinessType(String businessType) {
        if (!BusinessTypeEnum.isValid(businessType)) {
            throw exception(BUSINESS_PRODUCT_CONFIG_INVALID_BUSINESS_TYPE, businessType);
        }
    }

    private void validateProductSpu(Long productSpuId) {
        ProductSpuRespDTO spu = productSpuApi.getSpu(productSpuId);
        if (spu == null) {
            throw exception(BUSINESS_PRODUCT_CONFIG_NOT_EXISTS);
        }
    }

    private void validateBusinessProductUnique(String businessType, Long productSpuId, Long excludeId) {
        BusinessProductConfigDO existing = businessProductConfigMapper.selectByBusinessTypeAndProductSpuId(businessType, productSpuId);
        if (existing != null && !existing.getId().equals(excludeId)) {
            throw exception(BUSINESS_PRODUCT_CONFIG_EXISTS, businessType, productSpuId);
        }
    }

    private void handleDefaultConfig(BusinessProductConfigDO config) {
        if (Boolean.TRUE.equals(config.getIsDefault())) {
            // 取消同业务类型的其他默认配置
            BusinessProductConfigDO currentDefault = businessProductConfigMapper.selectDefaultByBusinessType(config.getBusinessType());
            if (currentDefault != null && !currentDefault.getId().equals(config.getId())) {
                currentDefault.setIsDefault(false);
                businessProductConfigMapper.updateById(currentDefault);
            }
        } else {
            // 如果是该业务类型的第一个配置，自动设为默认
            List<BusinessProductConfigDO> existingConfigs = businessProductConfigMapper.selectListByBusinessType(config.getBusinessType());
            if (CollUtil.isEmpty(existingConfigs)) {
                config.setIsDefault(true);
            }
        }
    }

    private AppBusinessConfigRespVO createEmptyAppBusinessConfigRespVO(String businessType) {
        AppBusinessConfigRespVO respVO = new AppBusinessConfigRespVO();
        respVO.setBusinessType(businessType);
        respVO.setBusinessName(BusinessProductConfigConvert.INSTANCE.getBusinessTypeName(businessType));
        respVO.setConfigs(Collections.emptyList());
        return respVO;
    }

    private void enrichPriceInfo(List<AppBusinessConfigRespVO.ConfigItem> configItems) {
        // TODO: 实现商品价格信息集成
        // 这里需要调用商品模块API获取价格信息，并根据用户会员等级计算会员价
        for (AppBusinessConfigRespVO.ConfigItem item : configItems) {
            AppBusinessConfigRespVO.PriceInfo priceInfo = new AppBusinessConfigRespVO.PriceInfo();
            // 临时设置示例数据，实际应该从商品模块获取
            priceInfo.setOriginalPrice(10000); // 100元
            priceInfo.setMemberPrice(7500);    // 75元
            priceInfo.setDiscountPercent(75);
            priceInfo.setMemberLevel("白银");
            item.setPriceInfo(priceInfo);
        }
    }

    private void enrichDetailPriceInfo(AppBusinessConfigDetailRespVO respVO) {
        // TODO: 实现商品价格信息集成
        AppBusinessConfigRespVO.PriceInfo priceInfo = new AppBusinessConfigRespVO.PriceInfo();
        priceInfo.setOriginalPrice(10000);
        priceInfo.setMemberPrice(7500);
        priceInfo.setDiscountPercent(75);
        priceInfo.setMemberLevel("白银");
        respVO.setPriceInfo(priceInfo);
    }

    private String getBusinessTypeDescription(BusinessTypeEnum typeEnum) {
        switch (typeEnum) {
            case DATA_CERTIFICATE_EVIDENCE:
                return "为数据资产提供知识产权保护的存证服务";
            case SOFTWARE_REGISTER:
                return "软件作品的著作权登记申请服务";
            case REAL_IDENTIFY:
                return "个人或企业的身份认证服务";
            case COPYRIGHT_REGISTER:
                return "文学、艺术等作品的著作权登记服务";
            case BLOCKCHAIN_EVIDENCE:
                return "基于区块链技术的数据存证服务";
            default:
                return typeEnum.getName();
        }
    }

}
