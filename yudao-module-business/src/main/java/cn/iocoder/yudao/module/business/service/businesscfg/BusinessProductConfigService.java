package cn.iocoder.yudao.module.business.service.businesscfg;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigCreateReqVO;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigPageReqVO;
import cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigUpdateReqVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigRespVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigDetailRespVO;
import cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessTypeRespVO;
import cn.iocoder.yudao.module.business.dal.dataobject.businesscfg.BusinessProductConfigDO;

import jakarta.validation.Valid;
import java.util.Collection;
import java.util.List;

/**
 * 业务商品配置 Service 接口
 *
 * <AUTHOR>
 */
public interface BusinessProductConfigService {

    /**
     * 创建业务商品配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBusinessProductConfig(@Valid BusinessProductConfigCreateReqVO createReqVO);

    /**
     * 更新业务商品配置
     *
     * @param updateReqVO 更新信息
     */
    void updateBusinessProductConfig(@Valid BusinessProductConfigUpdateReqVO updateReqVO);

    /**
     * 删除业务商品配置
     *
     * @param id 编号
     */
    void deleteBusinessProductConfig(Long id);

    /**
     * 获得业务商品配置
     *
     * @param id 编号
     * @return 业务商品配置
     */
    BusinessProductConfigDO getBusinessProductConfig(Long id);

    /**
     * 获得业务商品配置分页
     *
     * @param pageReqVO 分页查询
     * @return 业务商品配置分页
     */
    PageResult<BusinessProductConfigDO> getBusinessProductConfigPage(BusinessProductConfigPageReqVO pageReqVO);

    /**
     * 获得业务商品配置列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 业务商品配置列表
     */
    List<BusinessProductConfigDO> getBusinessProductConfigList(BusinessProductConfigPageReqVO exportReqVO);

    /**
     * 根据业务类型获取配置列表
     *
     * @param businessType 业务类型
     * @return 配置列表
     */
    List<BusinessProductConfigDO> getBusinessProductConfigListByType(String businessType);

    /**
     * 根据业务类型和状态获取配置列表
     *
     * @param businessType 业务类型
     * @param status 状态
     * @return 配置列表
     */
    List<BusinessProductConfigDO> getBusinessProductConfigListByTypeAndStatus(String businessType, String status);

    /**
     * 设置默认配置
     *
     * @param id 配置ID
     */
    void setDefaultConfig(Long id);

    /**
     * 获取默认配置
     *
     * @param businessType 业务类型
     * @return 默认配置
     */
    BusinessProductConfigDO getDefaultConfig(String businessType);

    /**
     * 校验业务商品配置是否存在
     *
     * @param id 编号
     * @return 业务商品配置
     */
    BusinessProductConfigDO validateBusinessProductConfigExists(Long id);

    /**
     * 校验商品SPU列表
     *
     * @param ids 商品SPU ID列表
     * @return 配置列表
     */
    List<BusinessProductConfigDO> validateSpuList(Collection<Long> ids);

    // ==================== 用户 App接口 ====================

    /**
     * 用户 App - 查询业务配置信息
     *
     * @param businessType 业务类型
     * @return 业务配置信息
     */
    AppBusinessConfigRespVO getAppBusinessConfigs(String businessType);

    /**
     * 用户 App - 获取配置详情
     *
     * @param configId 配置ID
     * @return 配置详情
     */
    AppBusinessConfigDetailRespVO getAppBusinessConfigDetail(Long configId);

    /**
     * 用户 App - 获取所有业务类型
     *
     * @return 业务类型列表
     */
    List<AppBusinessTypeRespVO> getAppBusinessTypes();

}
