package cn.iocoder.yudao.module.business.enums;

import cn.iocoder.yudao.framework.common.core.ArrayValuable;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 数据资产存证审核状态枚举
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CertificateReviewStatusEnum implements ArrayValuable<String> {

    DRAFT("A", "已提交"),
    PENDING("B", "审核中"),
    APPROVED("C", "审核通过"),
    REJECTED("D", "审核拒绝");

    public static final String[] ARRAYS = Arrays.stream(values()).map(CertificateReviewStatusEnum::getStatus).toArray(String[]::new);

    /**
     * 状态码
     */
    private final String status;
    /**
     * 状态名称
     */
    private final String name;

    @Override
    public String[] array() {
        return ARRAYS;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param status 状态码
     * @return 枚举
     */
    public static CertificateReviewStatusEnum getByStatus(String status) {
        for (CertificateReviewStatusEnum value : values()) {
            if (value.getStatus().equals(status)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 判断状态是否为审核通过
     *
     * @param status 状态码
     * @return 是否为审核通过
     */
    public static boolean isApproved(String status) {
        return APPROVED.getStatus().equals(status);
    }

    /**
     * 判断状态是否为待审核
     *
     * @param status 状态码
     * @return 是否为待审核
     */
    public static boolean isPending(String status) {
        return PENDING.getStatus().equals(status);
    }

    /**
     * 判断状态是否为审核不通过
     *
     * @param status 状态码
     * @return 是否为审核不通过
     */
    public static boolean isRejected(String status) {
        return REJECTED.getStatus().equals(status);
    }
}
