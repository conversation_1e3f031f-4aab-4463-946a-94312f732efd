package cn.iocoder.yudao.module.business.service.certificateinfo.impl;

import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.module.business.dal.dataobject.certificateinfo.CertificateInfoDO;
import cn.iocoder.yudao.module.business.dal.dataobject.identify.IdentityBlockchainLinkDO;
import cn.iocoder.yudao.module.business.dal.mysql.certificateinfo.CertificateInfoMapper;
import cn.iocoder.yudao.module.business.dal.mysql.identify.IdentityBlockchainLinkMapper;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.enums.CertificateReviewStatusEnum;
import cn.iocoder.yudao.module.business.service.certificateinfo.CertificateInfoAutoChainService;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftIssueService;
import cn.iocoder.yudao.module.business.service.blockchainlink.BusinessBlockchainLinkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 数据资产存证自动上链服务实现
 * 负责在审核通过后自动触发NFT发行上链
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class CertificateInfoAutoChainServiceImpl implements CertificateInfoAutoChainService {

    @Resource
    private CertificateInfoMapper certificateInfoMapper;

    @Resource
    private IdentityBlockchainLinkMapper identityBlockchainLinkMapper;

    @Resource
    private BusinessNftIssueService businessNftIssueService;

    @Resource
    private BusinessBlockchainLinkService businessBlockchainLinkService;

    @Override
    @Async
    public void handleCertificateReviewStatusChange(Integer certificateInfoId, String reviewStatus) {
        log.info("处理数据资产审核状态变更，存证ID: {}, 审核状态: {}", certificateInfoId, reviewStatus);

        // 只有审核通过时才触发自动上链
        if (!CertificateReviewStatusEnum.isApproved(reviewStatus)) {
            log.debug("数据资产审核状态不是通过状态，跳过自动上链，存证ID: {}, 状态: {}", 
                    certificateInfoId, reviewStatus);
            return;
        }

        // 检查是否已经上链
        if (isCertificateOnChain(certificateInfoId)) {
            log.warn("数据资产已经上链，跳过重复上链，存证ID: {}", certificateInfoId);
            return;
        }

        try {
            // 执行自动上链
            String result = issueNftForCertificate(certificateInfoId);
            log.info("数据资产自动上链成功，存证ID: {}, 结果: {}", certificateInfoId, result);
        } catch (Exception e) {
            log.error("数据资产自动上链失败，存证ID: {}", certificateInfoId, e);
            // 这里可以考虑添加重试机制或者发送告警通知
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String issueNftForCertificate(Integer certificateInfoId) {
        // 1. 获取数据资产存证信息
        CertificateInfoDO certificateInfo = certificateInfoMapper.selectById(certificateInfoId);
        if (certificateInfo == null) {
            throw new IllegalArgumentException("数据资产存证不存在，ID: " + certificateInfoId);
        }

        // 2. 获取创建者的区块链地址
        String creatorId = certificateInfo.getCreator();
        if (creatorId == null) {
            throw new IllegalArgumentException("数据资产存证创建者信息缺失，ID: " + certificateInfoId);
        }

        // 3. 查询创建者的区块链链接信息
        //IdentityBlockchainLinkDO blockchainLink = identityBlockchainLinkMapper.selectValidByIdentifyId(Long.valueOf(creatorId));
        IdentityBlockchainLinkDO blockchainLink = identityBlockchainLinkMapper.selectValidByPlatformUserId(creatorId);
        if (blockchainLink == null) {
            throw new IllegalArgumentException("创建者区块链链接信息不存在，用户ID: " + creatorId);
        }

        // 4. 检查区块链地址是否有效
        String address = blockchainLink.getAddress();
        if (address == null || address.trim().isEmpty()) {
            throw new IllegalArgumentException("创建者区块链地址无效，用户ID: " + creatorId);
        }

        try {
            // 5. 调用NFT发行服务
            log.info("开始为数据资产发行NFT，存证ID: {}, 创建者: {}, 区块链地址: {}",
                    certificateInfoId, creatorId, address);

            Map<String, String> nftResult = businessNftIssueService.issueNftForBusiness(
                    BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE,
                    certificateInfoId.longValue(),
                    address
            );

            log.info("数据资产NFT发行完成，存证ID: {}, 结果: {}", certificateInfoId, nftResult);

            // 6. 返回结果摘要
            String blockchainId = nftResult.get("blockchainId");
            String transactionHash = nftResult.get("transactionHash");
            return String.format("NFT发行成功 - 区块链ID: %s, 交易哈希: %s", blockchainId, transactionHash);

        } catch (Exception e) {
            log.error("数据资产NFT发行失败，存证ID: {}, 错误: {}", certificateInfoId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public boolean isCertificateOnChain(Integer certificateInfoId) {
        // 查询 business_blockchain_link 表中的上链记录
        // 这是实际的上链记录存储位置
        var blockchainLinks = businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                certificateInfoId.longValue(),
                BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode()
        );

        boolean isOnChain = !blockchainLinks.isEmpty();

        log.debug("检查数据资产是否已上链，存证ID: {}, business_blockchain_link记录数量: {}, 结果: {}",
                certificateInfoId, blockchainLinks.size(), isOnChain);

        return isOnChain;
    }
}
