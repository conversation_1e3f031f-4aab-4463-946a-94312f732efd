package cn.iocoder.yudao.module.business.controller.app.nft;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftIssueService;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftDataConverterFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 用户 App - 业务NFT发行控制器
 * 提供用户 App用户的NFT发行功能
 *
 * <AUTHOR>
 */
@Tag(name = "用户 App - 业务NFT发行")
@RestController
@RequestMapping("/app-api/business/nft")
@Slf4j
public class AppBusinessNftController {

    @Resource
    private BusinessNftIssueService businessNftIssueService;

    @Resource
    private BusinessNftDataConverterFactory converterFactory;

    @PostMapping("/issue")
    @Operation(summary = "发行业务NFT", description = "为指定业务类型和业务数据发行NFT")
    public CommonResult<Map<String, String>> issueNft(
            @Parameter(name = "businessType", description = "业务类型代码", required = true, example = "DATA_CERTIFICATE_EVIDENCE")
            @RequestParam("businessType") String businessTypeCode,
            @Parameter(name = "businessId", description = "业务数据ID", required = true, example = "1024")
            @RequestParam("businessId") Long businessId,
            @Parameter(name = "address", description = "区块链地址", required = true, example = "0x1234567890abcdef")
            @RequestParam("address") String address) {

        // 解析业务类型
        BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(businessTypeCode);
        if (businessType == null) {
            throw new IllegalArgumentException("不支持的业务类型: " + businessTypeCode);
        }

        Long userId = getLoginUserId();
        log.info("用户发起NFT发行，用户ID: {}, 业务类型: {}, 业务ID: {}, 地址: {}", 
                userId, businessType.getName(), businessId, address);

        Map<String, String> result = businessNftIssueService.issueNftForBusiness(businessType, businessId, address);
        
        log.info("NFT发行完成，用户ID: {}, 业务类型: {}, 业务ID: {}, 结果: {}", 
                userId, businessType.getName(), businessId, result);

        return success(result);
    }

    @GetMapping("/supported-types")
    @Operation(summary = "获取支持的业务类型", description = "获取所有支持NFT发行的业务类型列表")
    @PermitAll
    public CommonResult<List<BusinessTypeInfo>> getSupportedBusinessTypes() {
        List<BusinessTypeInfo> supportedTypes = List.of(BusinessTypeEnum.values())
                .stream()
                .map(businessType -> {
                    boolean supported = businessNftIssueService.isNftSupported(businessType);
                    Long categoryId = null;
                    if (supported) {
                        try {
                            // 通过转换器工厂获取类别ID，避免构建完整的WCDACDto
                            categoryId = getCategoryIdForBusinessType(businessType);
                        } catch (Exception e) {
                            log.warn("获取业务类型 {} 的类别ID失败: {}", businessType.getName(), e.getMessage());
                        }
                    }
                    return new BusinessTypeInfo(
                            businessType.getCode(),
                            businessType.getName(),
                            supported,
                            categoryId
                    );
                })
                .toList();

        return success(supportedTypes);
    }

    @GetMapping("/check-support")
    @Operation(summary = "检查业务类型是否支持NFT发行", description = "检查指定业务类型是否支持NFT发行")
    @PermitAll
    public CommonResult<Boolean> checkNftSupport(
            @Parameter(name = "businessType", description = "业务类型代码", required = true, example = "DATA_CERTIFICATE_EVIDENCE")
            @RequestParam("businessType") String businessTypeCode) {

        BusinessTypeEnum businessType = BusinessTypeEnum.getByCode(businessTypeCode);
        if (businessType == null) {
            return success(false);
        }

        boolean supported = businessNftIssueService.isNftSupported(businessType);
        return success(supported);
    }

    /**
     * 获取业务类型对应的类别ID
     */
    private Long getCategoryIdForBusinessType(BusinessTypeEnum businessType) {
        try {
            return converterFactory.getCategoryId(businessType);
        } catch (Exception e) {
            log.warn("无法获取业务类型 {} 的类别ID: {}", businessType.getName(), e.getMessage());
            return null;
        }
    }

    /**
     * 业务类型信息DTO
     */
    public static class BusinessTypeInfo {
        private String code;
        private String name;
        private boolean nftSupported;
        private Long categoryId;

        public BusinessTypeInfo(String code, String name, boolean nftSupported, Long categoryId) {
            this.code = code;
            this.name = name;
            this.nftSupported = nftSupported;
            this.categoryId = categoryId;
        }

        // Getters
        public String getCode() { return code; }
        public String getName() { return name; }
        public boolean isNftSupported() { return nftSupported; }
        public Long getCategoryId() { return categoryId; }

        // Setters
        public void setCode(String code) { this.code = code; }
        public void setName(String name) { this.name = name; }
        public void setNftSupported(boolean nftSupported) { this.nftSupported = nftSupported; }
        public void setCategoryId(Long categoryId) { this.categoryId = categoryId; }
    }
}
