#!/bin/bash

echo "开始检查业务商品配置功能编译..."

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_file() {
    local file=$1
    local desc=$2
    
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓${NC} $desc: $file"
        return 0
    else
        echo -e "${RED}✗${NC} $desc: $file (文件不存在)"
        return 1
    fi
}

echo -e "${YELLOW}1. 检查核心文件是否存在...${NC}"

# 检查枚举类
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/enums/BusinessTypeEnum.java" "业务类型枚举"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/enums/BusinessConfigStatusEnum.java" "配置状态枚举"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/enums/BusinessOrderStatusEnum.java" "订单状态枚举"

# 检查数据对象
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/dal/dataobject/businesscfg/BusinessProductConfigDO.java" "配置数据对象"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/dal/dataobject/businesscfg/BusinessOrderRelationDO.java" "订单关联数据对象"

# 检查Mapper
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/dal/mysql/businesscfg/BusinessProductConfigMapper.java" "配置Mapper"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/dal/mysql/businesscfg/BusinessOrderRelationMapper.java" "订单关联Mapper"

# 检查服务层
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/service/businesscfg/BusinessProductConfigService.java" "配置服务接口"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/service/businesscfg/BusinessProductConfigServiceImpl.java" "配置服务实现"

# 检查控制器
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/controller/admin/businesscfg/BusinessProductConfigController.java" "管理端控制器"
check_file "yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/controller/app/businesscfg/AppBusinessProductConfigController.java" "用户 App控制器"

# 检查数据库脚本
check_file "sql/mysql/business-product-config-2025-01-13.sql" "数据库脚本"

echo -e "${YELLOW}2. 检查依赖配置...${NC}"

# 检查pom.xml中的依赖
if grep -q "yudao-module-product" yudao-module-business/pom.xml; then
    echo -e "${GREEN}✓${NC} 商品模块依赖已添加"
else
    echo -e "${RED}✗${NC} 商品模块依赖未添加"
fi

echo -e "${YELLOW}3. 检查关键类的导入...${NC}"

# 检查ProductSpuApi导入
if grep -q "cn.iocoder.yudao.module.product.api.spu.ProductSpuApi" yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/service/businesscfg/BusinessProductConfigServiceImpl.java; then
    echo -e "${GREEN}✓${NC} ProductSpuApi导入正确"
else
    echo -e "${RED}✗${NC} ProductSpuApi导入有问题"
fi

echo -e "${YELLOW}4. 编译建议...${NC}"

echo "如果所有检查都通过，可以尝试编译："
echo "  mvn clean compile -pl yudao-module-business"
echo ""
echo "如果编译成功，可以继续执行："
echo "  1. 执行数据库脚本"
echo "  2. 重启应用"
echo "  3. 测试API接口"

echo -e "${GREEN}检查完成！${NC}"
