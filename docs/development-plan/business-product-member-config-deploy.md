# 业务商品配置功能开发计划

## 📋 项目概述
- **模块位置**：`yudao-module-business` 模块，按现有目录结构组织
- **权限范围**：
  - **Admin端**：仅管理员可用（配置管理）
  - **用户 App**：普通用户可用（查询业务配置信息）
- **需求文档**：`docs/product-requirements/business-product-member-config.md`

## 🗂️ 目录结构规划（按现有business模块结构）
```
yudao-module-business/src/main/java/cn/iocoder/yudao/module/business/
├── controller/
│   ├── admin/
│   │   └── businesscfg/
│   │       ├── BusinessProductConfigController.java
│   │       ├── BusinessOrderRelationController.java
│   │       └── vo/
│   │           ├── BusinessProductConfigBaseVO.java
│   │           ├── BusinessProductConfigCreateReqVO.java
│   │           ├── BusinessProductConfigUpdateReqVO.java
│   │           ├── BusinessProductConfigRespVO.java
│   │           ├── BusinessProductConfigPageReqVO.java
│   │           ├── BusinessOrderRelationRespVO.java
│   │           └── BusinessOrderRelationPageReqVO.java
│   └── app/
│       └── businesscfg/
│           ├── AppBusinessProductConfigController.java
│           └── vo/
│               ├── AppBusinessConfigQueryReqVO.java
│               ├── AppBusinessConfigRespVO.java
│               └── AppBusinessConfigDetailRespVO.java
├── convert/
│   └── businesscfg/
│       ├── BusinessProductConfigConvert.java
│       └── BusinessOrderRelationConvert.java
├── dal/
│   ├── dataobject/
│   │   └── businesscfg/
│   │       ├── BusinessProductConfigDO.java
│   │       └── BusinessOrderRelationDO.java
│   └── mysql/
│       └── businesscfg/
│           ├── BusinessProductConfigMapper.java
│           └── BusinessOrderRelationMapper.java
├── service/
│   └── businesscfg/
│       ├── BusinessProductConfigService.java
│       ├── BusinessProductConfigServiceImpl.java
│       ├── BusinessOrderRelationService.java
│       └── BusinessOrderRelationServiceImpl.java
└── enums/
    ├── BusinessTypeEnum.java
    ├── BusinessConfigStatusEnum.java
    └── BusinessOrderStatusEnum.java
```

## 📊 第一阶段：数据模型和基础服务（预计2天）

### 1.1 数据库表创建
- [ ] 在 `sql/mysql/` 目录下创建数据库迁移脚本
- [ ] 创建 `business_product_config` 表
- [ ] 添加必要的索引和约束

**表结构**：
```sql
CREATE TABLE business_product_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型',
    product_spu_id BIGINT NOT NULL COMMENT '商品SPU ID',
    config_name VARCHAR(100) NOT NULL COMMENT '配置名称',
    service_description TEXT COMMENT '服务说明',
    processing_days INT DEFAULT 3 COMMENT '处理周期（天）',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否为默认配置',
    status VARCHAR(20) DEFAULT 'ACTIVE' COMMENT '状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    creator VARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64) DEFAULT '',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BIT(1) DEFAULT b'0',
    tenant_id BIGINT NOT NULL DEFAULT 0,
    UNIQUE KEY uk_business_product (business_type, product_spu_id, tenant_id),
    UNIQUE KEY uk_business_default (business_type, tenant_id, is_default)
        COMMENT '同一业务类型只能有一个默认配置（通过应用层控制）',
    INDEX idx_business_type (business_type),
    INDEX idx_product_spu_id (product_spu_id),
    INDEX idx_status (status),
    INDEX idx_is_default (is_default)
) COMMENT='业务商品配置表';
```

**业务订单关联表结构**：
```sql
CREATE TABLE business_order_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    order_id BIGINT NOT NULL COMMENT '订单ID',
    business_type VARCHAR(50) NOT NULL COMMENT '业务类型',
    business_id BIGINT COMMENT '业务实体ID（业务完成后填入）',
    business_product_config_id BIGINT NOT NULL COMMENT '业务商品配置ID',
    business_status VARCHAR(20) DEFAULT 'PENDING' COMMENT '业务状态',
    business_data TEXT COMMENT '业务相关数据（JSON格式）',
    process_result TEXT COMMENT '处理结果',
    creator VARCHAR(64) DEFAULT '',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updater VARCHAR(64) DEFAULT '',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BIT(1) DEFAULT b'0',
    tenant_id BIGINT NOT NULL DEFAULT 0,
    UNIQUE KEY uk_order_id (order_id),
    INDEX idx_business_type (business_type),
    INDEX idx_business_product_config_id (business_product_config_id),
    INDEX idx_business_status (business_status),
    INDEX idx_tenant_id (tenant_id)
) COMMENT='业务订单关联表';
```

### 1.2 数据对象层（DAL）
- [ ] `dal/dataobject/businesscfg/BusinessProductConfigDO.java` - 数据对象
- [ ] `dal/mysql/businesscfg/BusinessProductConfigMapper.java` - MyBatis映射器
- [ ] 在 `resources/mapper/business/businesscfg/` 下创建 `BusinessProductConfigMapper.xml`
- [ ] `dal/dataobject/businesscfg/BusinessOrderRelationDO.java` - 业务订单关联数据对象
- [ ] `dal/mysql/businesscfg/BusinessOrderRelationMapper.java` - 业务订单关联映射器
- [ ] 在 `resources/mapper/business/businesscfg/` 下创建 `BusinessOrderRelationMapper.xml`

### 1.3 枚举类
- [ ] `enums/BusinessTypeEnum.java` - 业务类型枚举
- [ ] `enums/BusinessConfigStatusEnum.java` - 配置状态枚举
- [ ] `enums/BusinessOrderStatusEnum.java` - 业务订单状态枚举

### 1.4 订单类型扩展
- [ ] 扩展 `yudao-module-mall/yudao-module-trade-api/src/main/java/cn/iocoder/yudao/module/trade/enums/order/TradeOrderTypeEnum.java`
- [ ] 添加 `BUSINESS(5, "业务服务订单")` 类型

### 1.5 业务订单关联表SQL
- [ ] 在 `sql/mysql/` 下创建业务订单关联表的SQL脚本

### 1.6 VO对象设计

**管理端VO**（位于controller同层级vo文件夹）：
- [ ] `controller/admin/businesscfg/vo/BusinessProductConfigBaseVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessProductConfigCreateReqVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessProductConfigUpdateReqVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessProductConfigRespVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessProductConfigPageReqVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessOrderRelationRespVO.java`
- [ ] `controller/admin/businesscfg/vo/BusinessOrderRelationPageReqVO.java`

**用户 AppVO**（位于controller同层级vo文件夹）：
- [ ] `controller/app/businesscfg/vo/AppBusinessConfigQueryReqVO.java`
- [ ] `controller/app/businesscfg/vo/AppBusinessConfigRespVO.java`
- [ ] `controller/app/businesscfg/vo/AppBusinessConfigDetailRespVO.java`
- [ ] `controller/app/businesscfg/vo/AppBusinessTypeRespVO.java` - 业务类型响应VO

## 🔧 第二阶段：业务逻辑实现（预计2天）

### 2.1 服务层实现
- [ ] `service/businesscfg/BusinessProductConfigService.java` - 服务接口
- [ ] `service/businesscfg/BusinessProductConfigServiceImpl.java` - 服务实现
- [ ] `service/businesscfg/BusinessOrderRelationService.java` - 业务订单关联服务接口
- [ ] `service/businesscfg/BusinessOrderRelationServiceImpl.java` - 业务订单关联服务实现
- [ ] `service/businesscfg/BusinessPriceService.java` - 业务价格服务接口（集成商品价格）
- [ ] `service/businesscfg/BusinessPriceServiceImpl.java` - 业务价格服务实现

### 2.2 核心业务逻辑
- [ ] **默认配置唯一性验证**
- [ ] **自动设置默认配置**（新增时）
- [ ] **默认配置切换逻辑**
- [ ] **删除保护**（防止删除唯一默认配置）
- [ ] **用户 App配置查询逻辑**（含商品价格信息）
- [ ] **商品价格集成逻辑**（调用商品模块API）
- [ ] **业务类型枚举查询逻辑**
- [ ] **业务订单关联创建逻辑**
- [ ] **业务状态跟踪更新逻辑**
- [ ] **订单支付完成后立即创建业务实体**
- [ ] **用户权限验证逻辑**

**关键业务规则**：
1. 每个业务类型必须有且仅有一个默认配置
2. 新增配置时，如果是该业务类型的第一个配置，自动设为默认
3. 设置默认配置时，自动将同业务类型的其他配置设为非默认
4. 删除默认配置时，需要先设置其他配置为默认

### 2.3 对象转换
- [ ] `businesscfg/convert/BusinessProductConfigConvert.java` - 对象转换器
- [ ] `convert/businesscfg/BusinessOrderRelationConvert.java` - 业务订单关联转换器

## 🌐 第三阶段：控制器和API（预计1.5天）

### 3.1 管理端控制器
- [ ] `controller/admin/businesscfg/BusinessProductConfigController.java`
- [ ] `controller/admin/businesscfg/BusinessOrderRelationController.java`

**API接口**：
- [ ] `POST /admin-api/business/product-config` - 创建配置
- [ ] `PUT /admin-api/business/product-config/{id}` - 更新配置
- [ ] `DELETE /admin-api/business/product-config/{id}` - 删除配置
- [ ] `GET /admin-api/business/product-config/{id}` - 获取配置详情
- [ ] `GET /admin-api/business/product-config/page` - 分页查询
- [ ] `GET /admin-api/business/product-config/list-by-type` - 按业务类型查询
- [ ] `PUT /admin-api/business/product-config/{id}/set-default` - 设置默认配置

**业务订单关联管理接口**：
- [ ] `GET /admin-api/business/order-relation/page` - 分页查询业务订单关联
- [ ] `GET /admin-api/business/order-relation/{id}` - 获取业务订单关联详情
- [ ] `PUT /admin-api/business/order-relation/{id}/status` - 更新业务状态

### 3.2 用户 App控制器
- [ ] `controller/app/businesscfg/AppBusinessProductConfigController.java`

**API接口**：
- [ ] `GET /app-api/business/config/query` - **查询业务配置信息**
  - **权限**：需要用户登录
  - **参数**：`businessType`（必传）
  - **返回**：该业务类型的所有激活配置，默认配置排在第一位，包含商品价格信息
- [ ] `GET /app-api/business/config/detail/{configId}` - 获取配置详情
  - **权限**：需要用户登录
  - **返回**：配置详情和商品价格信息
- [ ] `GET /app-api/business/config/business-types` - **获取所有业务类型**
  - **权限**：需要用户登录
  - **返回**：所有可用的业务类型列表

### 3.3 订单类型扩展和业务信息处理
- [ ] **扩展TradeOrderTypeEnum**：添加BUSINESS类型
- [ ] **订单业务信息存储**：使用订单表的userRemark字段存储业务信息（格式：businessType|businessData）
- [ ] **业务订单处理器**：实现TradeOrderHandler接口，支付完成后立即创建业务实体
- [ ] **业务状态同步**：订单状态变更时同步更新业务状态
- [ ] **商品价格集成**：调用商品模块API获取实时价格信息

### 3.4 权限配置和错误码
- [ ] 在 `enums/ErrorCodeConstants.java` 中添加相关错误码
- [ ] 配置管理端权限：`business:product-config:*`、`business:order-relation:*`
- [ ] **用户 App权限配置**：需要用户登录，使用`@PreAuthenticated`注解
- [ ] **商品价格集成**：集成商品模块API获取价格信息

## 🧪 第四阶段：测试和验证（预计1.5天）

### 4.1 单元测试
- [ ] Service层单元测试
- [ ] 默认配置业务逻辑测试
- [ ] 用户 App查询逻辑测试
- [ ] 边界条件测试

### 4.2 集成测试
- [ ] 管理端Controller集成测试
- [ ] 用户 AppController集成测试
- [ ] API接口测试
- [ ] 权限验证测试

### 4.3 业务场景测试
- [ ] **前端集成测试**：验证用户 App返回数据格式
- [ ] **多配置展示测试**：验证默认配置排序
- [ ] **业务类型覆盖测试**：验证所有业务类型都有配置

## 📋 关键技术要点

### 1. 业务类型枚举
```java
public enum BusinessTypeEnum {
    DATA_CERTIFICATE_EVIDENCE("data_certificate_evidence", "数据知识产权存证"),
    SOFTWARE_REGISTER("software_register", "软件著作权登记"),
    REAL_IDENTIFY("real_identify", "实名认证"),
    COPYRIGHT_REGISTER("copyright_register", "作品著作权登记"),
    BLOCKCHAIN_EVIDENCE("blockchain_evidence", "区块链存证");
}
```

### 2. 订单类型扩展
```java
// 在 TradeOrderTypeEnum 中添加
public enum TradeOrderTypeEnum {
    NORMAL(0, "普通订单"),
    SECKILL(1, "秒杀订单"),
    BARGAIN(2, "砍价订单"),
    COMBINATION(3, "拼团订单"),
    POINT(4, "积分商城"),
    BUSINESS(5, "业务服务订单");  // 新增业务订单类型
}
```

### 3. 业务订单状态枚举
```java
public enum BusinessOrderStatusEnum {
    PENDING("PENDING", "待处理"),
    PROCESSING("PROCESSING", "处理中"),
    COMPLETED("COMPLETED", "已完成"),
    FAILED("FAILED", "处理失败"),
    CANCELLED("CANCELLED", "已取消");
}
```

### 4. 业务订单处理器设计
```java
// 实现 TradeOrderHandler 接口
@Component
public class BusinessOrderHandler implements TradeOrderHandler {

    @Override
    public void beforeOrderCreate(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 验证业务订单的特殊逻辑
        // 解析userRemark中的业务信息
        // 验证业务类型和商品配置的有效性
    }

    @Override
    public void afterPayOrder(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 支付完成后立即执行：
        // 1. 创建业务订单关联记录
        // 2. 立即创建业务实体（数据存证、软著登记等）
        // 3. 更新业务状态为PROCESSING
        // 4. 发送业务处理通知
    }

    @Override
    public void afterCancelOrder(TradeOrderDO order, List<TradeOrderItemDO> orderItems) {
        // 取消订单时更新业务状态为CANCELLED
        // 如果业务实体已创建，则标记为取消状态
    }
}
```

### 5. 业务信息存储格式
```java
// 在订单创建时，将业务信息存储在userRemark字段中
// 格式：businessType|businessData
// 示例：
"data_certificate_evidence|{\"dataName\":\"测试数据\",\"dataDescription\":\"用于测试的数据存证\",\"contactInfo\":\"联系方式\"}"
```

### 5. 用户 AppAPI设计示例

**查询业务配置信息**（含商品价格）：
```http
GET /app-api/business/config/query?businessType=data_certificate_evidence
Authorization: Bearer {token}

Response:
{
    "code": 0,
    "data": {
        "businessType": "data_certificate_evidence",
        "businessName": "数据知识产权存证",
        "configs": [
            {
                "configId": 1,
                "configName": "数据存证基础版",
                "serviceDescription": "标准数据存证服务，7天完成",
                "processingDays": 7,
                "isDefault": true,
                "productSpuId": 123,
                "sortOrder": 1,
                "status": "ACTIVE",
                "priceInfo": {
                    "originalPrice": 10000,
                    "memberPrice": 7500,
                    "discountPercent": 75,
                    "memberLevel": "白银"
                }
            },
            {
                "configId": 2,
                "configName": "数据存证标准版",
                "serviceDescription": "加急数据存证服务，3天完成",
                "processingDays": 3,
                "isDefault": false,
                "productSpuId": 1002,
                "sortOrder": 2,
                "status": "ACTIVE",
                "priceInfo": {
                    "originalPrice": 20000,
                    "memberPrice": 15000,
                    "discountPercent": 75,
                    "memberLevel": "白银"
                }
            }
        ]
    }
}
```

**获取所有业务类型**：
```http
GET /app-api/business/config/business-types
Authorization: Bearer {token}

Response:
{
    "code": 0,
    "data": [
        {
            "code": "data_certificate_evidence",
            "name": "数据知识产权存证",
            "description": "为数据资产提供知识产权保护的存证服务"
        },
        {
            "code": "software_register",
            "name": "软件著作权登记",
            "description": "软件作品的著作权登记申请服务"
        },
        {
            "code": "real_identify",
            "name": "实名认证",
            "description": "个人或企业的身份认证服务"
        },
        {
            "code": "copyright_register",
            "name": "作品著作权登记",
            "description": "文学、艺术等作品的著作权登记服务"
        },
        {
            "code": "blockchain_evidence",
            "name": "区块链存证",
            "description": "基于区块链技术的数据存证服务"
        }
    ]
}
```

### 3. 默认配置业务逻辑
```java
// 用户 App查询逻辑
public List<AppBusinessConfigRespVO> getBusinessConfigs(String businessType) {
    // 1. 查询该业务类型的所有激活配置
    List<BusinessProductConfigDO> configs = mapper.selectByBusinessTypeAndStatus(
        businessType, BusinessConfigStatusEnum.ACTIVE);
    
    // 2. 按默认配置和排序字段排序
    configs.sort((a, b) -> {
        if (a.getIsDefault() && !b.getIsDefault()) return -1;
        if (!a.getIsDefault() && b.getIsDefault()) return 1;
        return a.getSortOrder().compareTo(b.getSortOrder());
    });
    
    // 3. 转换为用户 AppVO
    return BusinessProductConfigConvert.INSTANCE.convertToAppList(configs);
}
```

## 📁 文件路径示例

**数据库脚本**：
```
sql/mysql/business-product-config-2025-01-13.sql
```

**Mapper XML**：
```
yudao-module-business/src/main/resources/mapper/business/businesscfg/BusinessProductConfigMapper.xml
```

**完整类路径示例**：
```java
// 数据对象
cn.iocoder.yudao.module.business.dal.dataobject.businesscfg.BusinessProductConfigDO
cn.iocoder.yudao.module.business.dal.dataobject.businesscfg.BusinessOrderRelationDO

// 服务接口
cn.iocoder.yudao.module.business.service.businesscfg.BusinessProductConfigService
cn.iocoder.yudao.module.business.service.businesscfg.BusinessOrderRelationService

// 管理端控制器
cn.iocoder.yudao.module.business.controller.admin.businesscfg.BusinessProductConfigController
cn.iocoder.yudao.module.business.controller.admin.businesscfg.BusinessOrderRelationController

// 用户 App控制器
cn.iocoder.yudao.module.business.controller.app.businesscfg.AppBusinessProductConfigController

// 管理端VO
cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigRespVO
cn.iocoder.yudao.module.business.controller.admin.businesscfg.vo.BusinessProductConfigPageReqVO

// 用户 AppVO
cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigRespVO
cn.iocoder.yudao.module.business.controller.app.businesscfg.vo.AppBusinessConfigDetailRespVO

// 枚举类
cn.iocoder.yudao.module.business.enums.BusinessTypeEnum
cn.iocoder.yudao.module.business.enums.BusinessConfigStatusEnum
cn.iocoder.yudao.module.business.enums.BusinessOrderStatusEnum
```

## 📅 时间安排
- **总计**：8个工作日
- **第1-2天**：数据模型和基础服务（含订单类型扩展）
- **第3-4天**：业务逻辑实现（含业务订单关联逻辑）
- **第5-6天**：控制器和API（含业务订单处理器）
- **第7天**：订单集成和业务流程测试
- **第8天**：系统集成测试和验证

## ✅ 已确认的需求

1. **用户 App权限**：✅ 用户 App查询接口需要用户登录
2. **商品价格集成**：✅ 用户 App需要同时返回商品价格信息
3. **业务类型枚举**：✅ 需要提供获取所有业务类型的接口
4. **数据格式**：✅ 用户 App返回的配置信息格式满足前端需求
5. **订单类型扩展验证**：✅ 确认TradeOrderTypeEnum是通过代码枚举实现
6. **业务信息存储方式**：✅ 确认使用订单表的userRemark字段存储业务信息
7. **业务实体创建时机**：✅ 在订单支付完成后立即创建

## ❓ 待确认的问题

1. **缓存策略**：业务配置信息是否需要缓存（配置变更不频繁）？
2. **业务状态同步机制**：业务状态变更是通过消息队列异步处理，还是同步调用？

---

**文档版本**：v2.0（需求确认版）
**创建日期**：2025-01-13
**最后更新**：2025-01-13
**文档状态**：需求已确认，可执行开发
