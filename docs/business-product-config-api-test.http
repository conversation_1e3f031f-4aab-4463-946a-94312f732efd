### 业务商品配置功能API测试文件
### 使用前请先配置环境变量

### 环境变量配置
@adminApi = http://127.0.0.1:48080/admin-api
@appApi = http://127.0.0.1:48080/app-api
@adminToken = your_admin_token_here
@appToken = your_app_token_here
@tenantId = 1

### ========== 管理端接口测试 ==========

### 1. 创建业务商品配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "data_certificate_evidence",
    "productSpuId": 1,
    "configName": "数据存证基础版",
    "serviceDescription": "标准数据存证服务，7天完成",
    "processingDays": 7,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 2. 获取业务商品配置详情
GET {{adminApi}}/business/product-config/get?id=1
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 3. 分页查询业务商品配置
GET {{adminApi}}/business/product-config/page?pageNo=1&pageSize=10&businessType=data_certificate_evidence
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 4. 根据业务类型查询配置列表
GET {{adminApi}}/business/product-config/list-by-type?businessType=data_certificate_evidence
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 5. 设置默认配置
PUT {{adminApi}}/business/product-config/set-default/1
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 6. 更新业务商品配置
PUT {{adminApi}}/business/product-config/update
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "id": 1,
    "businessType": "data_certificate_evidence",
    "productSpuId": 1,
    "configName": "数据存证基础版（更新）",
    "serviceDescription": "标准数据存证服务，7天完成（已更新）",
    "processingDays": 5,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 7. 删除业务商品配置
DELETE {{adminApi}}/business/product-config/delete?id=1
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### ========== 业务订单关联管理接口 ==========

### 8. 获取业务订单关联详情
GET {{adminApi}}/business/order-relation/get?id=1
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 9. 分页查询业务订单关联
GET {{adminApi}}/business/order-relation/page?pageNo=1&pageSize=10
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 10. 根据订单ID获取业务订单关联
GET {{adminApi}}/business/order-relation/get-by-order?orderId=1001
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### 11. 更新业务状态
PUT {{adminApi}}/business/order-relation/update-status/1?businessStatus=PROCESSING&processResult=开始处理业务
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

### ========== 用户 App接口测试 ==========

### 12. 查询业务配置信息（含价格）
GET {{appApi}}/business/config/query?businessType=data_certificate_evidence
Authorization: Bearer {{appToken}}
tenant-id: {{tenantId}}

### 13. 获取配置详情
GET {{appApi}}/business/config/detail/1
Authorization: Bearer {{appToken}}
tenant-id: {{tenantId}}

### 14. 获取所有业务类型
GET {{appApi}}/business/config/business-types
Authorization: Bearer {{appToken}}
tenant-id: {{tenantId}}

### ========== 测试数据创建 ==========

### 15. 创建软著登记配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "software_register",
    "productSpuId": 2,
    "configName": "软著登记标准版",
    "serviceDescription": "软件著作权登记服务，15天完成",
    "processingDays": 15,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 16. 创建实名认证配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "real_identify",
    "productSpuId": 3,
    "configName": "个人实名认证",
    "serviceDescription": "个人身份认证服务，1天完成",
    "processingDays": 1,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 17. 创建版权登记配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "copyright_register",
    "productSpuId": 4,
    "configName": "作品著作权登记",
    "serviceDescription": "文学艺术作品著作权登记，10天完成",
    "processingDays": 10,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 18. 创建区块链存证配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "blockchain_evidence",
    "productSpuId": 5,
    "configName": "区块链存证服务",
    "serviceDescription": "基于区块链的数据存证，即时完成",
    "processingDays": 0,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### ========== 错误测试用例 ==========

### 19. 测试无效业务类型
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "invalid_type",
    "productSpuId": 1,
    "configName": "测试配置",
    "serviceDescription": "测试描述",
    "processingDays": 7,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}

### 20. 测试重复配置
POST {{adminApi}}/business/product-config/create
Content-Type: application/json
Authorization: Bearer {{adminToken}}
tenant-id: {{tenantId}}

{
    "businessType": "data_certificate_evidence",
    "productSpuId": 1,
    "configName": "重复配置",
    "serviceDescription": "这是一个重复的配置",
    "processingDays": 7,
    "isDefault": false,
    "status": "ACTIVE",
    "sortOrder": 2
}
