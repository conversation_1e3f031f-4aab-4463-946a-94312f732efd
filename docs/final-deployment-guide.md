# 业务商品配置功能 - 最终部署指南

## 🎯 快速部署步骤

### 第一步：依赖检查与配置

1. **检查商品模块依赖**
   ```bash
   # 确认yudao-module-business/pom.xml中包含以下依赖
   grep -A 4 "yudao-module-product" yudao-module-business/pom.xml
   ```
   
   应该看到：
   ```xml
   <dependency>
       <groupId>cn.iocoder.boot</groupId>
       <artifactId>yudao-module-product</artifactId>
       <version>${revision}</version>
   </dependency>
   ```

2. **编译验证**
   ```bash
   # 先编译商品模块（如果需要）
   mvn clean install -pl yudao-module-mall/yudao-module-product
   
   # 编译业务模块
   mvn clean compile -pl yudao-module-business
   ```

### 第二步：数据库部署

1. **执行建表脚本**
   ```sql
   -- 连接数据库
   USE wenchaung_chain_db;
   
   -- 执行脚本
   SOURCE sql/mysql/business-product-config-2025-01-13.sql;
   
   -- 验证表创建
   SHOW TABLES LIKE 'business_%';
   ```

2. **验证数据**
   ```sql
   -- 检查配置表
   SELECT COUNT(*) FROM business_product_config;
   SELECT * FROM business_product_config LIMIT 3;
   
   -- 检查关联表
   DESC business_order_relation;
   ```

### 第三步：应用部署

1. **重启应用**
   ```bash
   # 停止应用
   ./stop.sh
   
   # 启动应用
   ./start.sh
   
   # 检查启动日志
   tail -f logs/application.log | grep -i "business"
   ```

2. **验证接口注册**
   ```bash
   # 检查管理端接口
   curl -X GET "http://localhost:48080/admin-api/business/product-config/page?pageNo=1&pageSize=10" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "tenant-id: 1"
   
   # 检查用户 App接口
   curl -X GET "http://localhost:48080/app-api/business/config/business-types" \
        -H "Authorization: Bearer YOUR_TOKEN" \
        -H "tenant-id: 1"
   ```

### 第四步：功能测试

1. **访问Swagger文档**
   ```
   http://localhost:48080/swagger-ui/index.html
   ```

   在API分组中应该能看到"business"分组，包含17个接口。

2. **使用API测试文件**
   使用提供的API测试文件 `docs/business-product-config-api-test.http` 进行完整测试。

## 🔧 常见问题解决

### 问题1：编译错误 - 找不到ProductSpuApi

**错误信息**：
```
java: 程序包cn.iocoder.yudao.module.product.api.spu不存在
```

**解决方案**：
1. 检查pom.xml依赖是否正确添加
2. 先编译product模块：`mvn clean install -pl yudao-module-mall/yudao-module-product`
3. 再编译business模块：`mvn clean compile -pl yudao-module-business`

### 问题1.1：启动错误 - MyBatis找不到DO类

**错误信息**：
```
java.lang.ClassNotFoundException: Cannot find class: cn.iocoder.yudao.module.business.dal.dataobject.businesscfg.BusinessOrderRelationDO
```

**解决方案**：
1. 这是XML映射文件中resultMap定义导致的问题
2. 已修复：简化了XML文件，移除了不必要的resultMap定义
3. 重新编译：`mvn clean compile -pl yudao-module-business`

### 问题2：数据库表已存在

**错误信息**：
```
Table 'business_product_config' already exists
```

**解决方案**：
```sql
-- 删除现有表（注意备份数据）
DROP TABLE IF EXISTS business_order_relation;
DROP TABLE IF EXISTS business_product_config;

-- 重新执行脚本
SOURCE sql/mysql/business-product-config-2025-01-13.sql;
```

### 问题3：权限不足

**错误信息**：
```
403 Forbidden
```

**解决方案**：
1. 确认用户已登录
2. 检查用户是否有对应权限
3. 确认权限标识配置正确

### 问题4：接口404

**错误信息**：
```
404 Not Found
```

**解决方案**：
1. 检查应用是否正常启动
2. 确认Controller路径映射
3. 验证接口URL是否正确

## ✅ 部署验证清单

### 编译验证
- [ ] business模块编译成功
- [ ] 无import错误
- [ ] 无语法错误

### 数据库验证
- [ ] 表结构创建成功
- [ ] 示例数据插入成功
- [ ] 索引和约束正确

### 应用验证
- [ ] 应用正常启动
- [ ] 无错误日志
- [ ] 接口路径正确注册

### 功能验证
- [ ] 管理端接口正常
- [ ] 用户 App接口正常
- [ ] 权限控制正确
- [ ] 业务逻辑正确

## 📋 API接口快速测试

### 1. 创建配置（管理端）
```bash
curl -X POST "http://localhost:48080/admin-api/business/product-config/create" \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_ADMIN_TOKEN" \
     -H "tenant-id: 1" \
     -d '{
       "businessType": "data_certificate_evidence",
       "productSpuId": 1,
       "configName": "数据存证基础版",
       "serviceDescription": "标准数据存证服务，7天完成",
       "processingDays": 7,
       "isDefault": true,
       "status": "ACTIVE",
       "sortOrder": 1
     }'
```

### 2. 查询配置（用户 App）
```bash
curl -X GET "http://localhost:48080/app-api/business/config/query?businessType=data_certificate_evidence" \
     -H "Authorization: Bearer YOUR_APP_TOKEN" \
     -H "tenant-id: 1"
```

### 3. 获取业务类型（用户 App）
```bash
curl -X GET "http://localhost:48080/app-api/business/config/business-types" \
     -H "Authorization: Bearer YOUR_APP_TOKEN" \
     -H "tenant-id: 1"
```

## 🎉 部署成功标志

当看到以下结果时，说明部署成功：

1. **编译成功**：无任何编译错误
2. **应用启动**：日志中无ERROR级别错误
3. **接口响应**：API测试返回正确的JSON响应
4. **数据库**：表中有示例数据

## 📞 技术支持

如果遇到问题，请按以下顺序排查：

1. **查看日志**：`tail -f logs/application.log`
2. **检查配置**：确认pom.xml和数据库配置
3. **验证权限**：确认用户权限和接口权限
4. **重新部署**：按照本指南重新执行部署步骤

---

**部署完成确认**：
- 部署人员：_______________
- 部署时间：_______________
- 验证结果：_______________
