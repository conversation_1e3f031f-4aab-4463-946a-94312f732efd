# 业务商品配置功能部署检查清单

## 📋 部署前检查

### 1. 代码检查
- [ ] 所有Java文件编译通过
- [ ] 没有语法错误和导入错误
- [ ] 所有依赖项正确配置

### 2. 数据库准备
- [ ] 执行数据库脚本：`sql/mysql/business-product-config-2025-01-13.sql`
- [ ] 确认表结构创建成功
- [ ] 验证示例数据插入成功
- [ ] 检查索引和约束是否正确创建

### 3. 权限配置
- [ ] 配置管理端权限菜单
- [ ] 添加以下权限标识：
  - `business:product-config:create`
  - `business:product-config:update`
  - `business:product-config:delete`
  - `business:product-config:query`
  - `business:product-config:export`
  - `business:order-relation:query`
  - `business:order-relation:update`

## 🚀 部署步骤

### 1. 代码部署
```bash
# 1. 备份现有代码
cp -r yudao-module-business yudao-module-business.backup

# 2. 部署新代码
# 将生成的所有文件复制到对应目录

# 3. 编译检查
mvn clean compile -pl yudao-module-business
```

### 2. 数据库部署
```sql
-- 1. 连接数据库
USE wenchaung_chain_db;

-- 2. 执行建表脚本
SOURCE sql/mysql/business-product-config-2025-01-13.sql;

-- 3. 验证表结构
SHOW TABLES LIKE 'business_%';
DESC business_product_config;
DESC business_order_relation;

-- 4. 验证示例数据
SELECT COUNT(*) FROM business_product_config;
```

### 3. 应用重启
```bash
# 1. 停止应用
./stop.sh

# 2. 清理缓存
rm -rf logs/*
rm -rf temp/*

# 3. 启动应用
./start.sh

# 4. 检查启动日志
tail -f logs/application.log
```

## ✅ 部署后验证

### 1. 基础功能验证
- [ ] 应用正常启动，无错误日志
- [ ] 数据库连接正常
- [ ] 所有接口路径正确注册

### 2. 管理端接口验证
使用 `docs/business-product-config-api-test.http` 测试：

- [ ] 创建业务商品配置接口
- [ ] 查询配置详情接口
- [ ] 分页查询接口
- [ ] 更新配置接口
- [ ] 删除配置接口
- [ ] 设置默认配置接口

### 3. 用户 App接口验证
- [ ] 查询业务配置信息接口
- [ ] 获取配置详情接口
- [ ] 获取所有业务类型接口

### 4. 权限验证
- [ ] 管理端接口需要正确权限
- [ ] 用户 App接口需要用户登录
- [ ] 无权限访问返回正确错误码

### 5. 业务逻辑验证
- [ ] 默认配置唯一性验证
- [ ] 业务类型有效性验证
- [ ] 商品SPU存在性验证
- [ ] 数据完整性约束验证

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 编译错误
**问题**：找不到包或类
**解决**：
```bash
# 检查依赖
mvn dependency:tree -pl yudao-module-business

# 重新编译
mvn clean install -pl yudao-framework/yudao-spring-boot-starter-mybatis
mvn clean compile -pl yudao-module-business
```

#### 2. 数据库连接错误
**问题**：表不存在或字段不匹配
**解决**：
```sql
-- 检查表结构
SHOW CREATE TABLE business_product_config;

-- 重新执行建表脚本
DROP TABLE IF EXISTS business_product_config;
DROP TABLE IF EXISTS business_order_relation;
SOURCE sql/mysql/business-product-config-2025-01-13.sql;
```

#### 3. 权限错误
**问题**：接口返回403权限不足
**解决**：
- 检查用户是否有对应权限
- 确认权限标识配置正确
- 验证角色权限分配

#### 4. 接口404错误
**问题**：接口路径不存在
**解决**：
- 检查Controller注解路径
- 确认应用正常启动
- 验证接口映射注册

## 📊 监控指标

### 1. 性能监控
- [ ] 接口响应时间 < 500ms
- [ ] 数据库查询时间 < 100ms
- [ ] 内存使用正常

### 2. 业务监控
- [ ] 配置创建成功率
- [ ] 默认配置设置正确性
- [ ] 业务类型覆盖完整性

### 3. 错误监控
- [ ] 无系统异常日志
- [ ] 业务异常正确处理
- [ ] 用户操作审计完整

## 📝 回滚计划

如果部署出现问题，按以下步骤回滚：

### 1. 代码回滚
```bash
# 恢复备份代码
rm -rf yudao-module-business
mv yudao-module-business.backup yudao-module-business

# 重新编译
mvn clean compile -pl yudao-module-business
```

### 2. 数据库回滚
```sql
-- 删除新增表（如果需要）
DROP TABLE IF EXISTS business_order_relation;
DROP TABLE IF EXISTS business_product_config;

-- 恢复数据库备份（如果有）
-- SOURCE backup/database_backup.sql;
```

### 3. 应用重启
```bash
./stop.sh
./start.sh
```

## 📞 联系信息

如果在部署过程中遇到问题，请联系：
- 开发团队：[开发团队联系方式]
- 运维团队：[运维团队联系方式]
- 紧急联系：[紧急联系方式]

---

**部署完成确认**：
- 部署人员：_______________
- 测试人员：_______________
- 部署时间：_______________
- 验证时间：_______________
