# 业务商品配置功能使用说明

## 功能概述

业务商品配置功能是连接业务服务与商品销售的核心桥梁，通过建立业务实体与商品的关联关系，实现业务服务的产品化销售和统一管理。

## 主要特性

- **业务产品化**：将专业服务转化为标准化商品
- **收费标准化**：建立统一的收费管理体系
- **数据一体化**：实现业务数据与商品数据的有机结合
- **多端支持**：提供管理端和用户 App完整的API接口

## 支持的业务类型

1. **数据知识产权存证** (`data_certificate_evidence`)
2. **软件著作权登记** (`software_register`)
3. **实名认证** (`real_identify`)
4. **作品著作权登记** (`copyright_register`)
5. **区块链存证** (`blockchain_evidence`)

## 数据库表结构

### 业务商品配置表 (business_product_config)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| business_type | VARCHAR(50) | 业务类型 |
| product_spu_id | BIGINT | 商品SPU ID |
| config_name | VARCHAR(100) | 配置名称 |
| service_description | TEXT | 服务说明 |
| processing_days | INT | 处理周期（天） |
| is_default | BOOLEAN | 是否为默认配置 |
| status | VARCHAR(20) | 状态 |
| sort_order | INT | 排序 |

### 业务订单关联表 (business_order_relation)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| order_id | BIGINT | 订单ID |
| business_type | VARCHAR(50) | 业务类型 |
| business_id | BIGINT | 业务实体ID |
| business_product_config_id | BIGINT | 业务商品配置ID |
| business_status | VARCHAR(20) | 业务状态 |
| business_data | TEXT | 业务相关数据（JSON格式） |
| process_result | TEXT | 处理结果 |

## API接口说明

### 管理端接口

#### 业务商品配置管理

- `POST /admin-api/business/product-config` - 创建配置
- `PUT /admin-api/business/product-config` - 更新配置
- `DELETE /admin-api/business/product-config` - 删除配置
- `GET /admin-api/business/product-config/{id}` - 获取配置详情
- `GET /admin-api/business/product-config/page` - 分页查询
- `GET /admin-api/business/product-config/list-by-type` - 按业务类型查询
- `PUT /admin-api/business/product-config/set-default/{id}` - 设置默认配置

#### 业务订单关联管理

- `GET /admin-api/business/order-relation/page` - 分页查询业务订单关联
- `GET /admin-api/business/order-relation/{id}` - 获取业务订单关联详情
- `PUT /admin-api/business/order-relation/update-status/{id}` - 更新业务状态

### 用户 App接口

- `GET /app-api/business/config/query` - 查询业务配置信息
- `GET /app-api/business/config/detail/{configId}` - 获取配置详情
- `GET /app-api/business/config/business-types` - 获取所有业务类型

## 使用示例

### 1. 创建业务商品配置

```json
POST /admin-api/business/product-config
{
    "businessType": "data_certificate_evidence",
    "productSpuId": 123,
    "configName": "数据存证基础版",
    "serviceDescription": "标准数据存证服务，7天完成",
    "processingDays": 7,
    "isDefault": true,
    "status": "ACTIVE",
    "sortOrder": 1
}
```

### 2. 用户 App查询业务配置

```http
GET /app-api/business/config/query?businessType=data_certificate_evidence
Authorization: Bearer {token}
```

响应示例：
```json
{
    "code": 0,
    "data": {
        "businessType": "data_certificate_evidence",
        "businessName": "数据知识产权存证",
        "configs": [
            {
                "configId": 1,
                "configName": "数据存证基础版",
                "serviceDescription": "标准数据存证服务，7天完成",
                "processingDays": 7,
                "isDefault": true,
                "productSpuId": 123,
                "sortOrder": 1,
                "status": "ACTIVE",
                "priceInfo": {
                    "originalPrice": 10000,
                    "memberPrice": 7500,
                    "discountPercent": 75,
                    "memberLevel": "白银"
                }
            }
        ]
    }
}
```

### 3. 获取所有业务类型

```http
GET /app-api/business/config/business-types
Authorization: Bearer {token}
```

响应示例：
```json
{
    "code": 0,
    "data": [
        {
            "code": "data_certificate_evidence",
            "name": "数据知识产权存证",
            "description": "为数据资产提供知识产权保护的存证服务"
        },
        {
            "code": "software_register",
            "name": "软件著作权登记",
            "description": "软件作品的著作权登记申请服务"
        }
    ]
}
```

## 核心业务逻辑

### 默认配置管理

1. 每个业务类型必须有且仅有一个默认配置
2. 新增配置时，如果是该业务类型的第一个配置，自动设为默认
3. 设置默认配置时，自动将同业务类型的其他配置设为非默认
4. 删除默认配置时，需要先设置其他配置为默认

### 订单业务流程

1. 用户下单时，在订单备注中存储业务信息
2. 订单支付完成后，自动创建业务订单关联记录
3. 立即创建对应的业务实体（数据存证、软著登记等）
4. 更新业务状态为"处理中"
5. 业务完成后更新状态为"已完成"

## 权限配置

### 管理端权限

- `business:product-config:create` - 创建业务商品配置
- `business:product-config:update` - 更新业务商品配置
- `business:product-config:delete` - 删除业务商品配置
- `business:product-config:query` - 查询业务商品配置
- `business:product-config:export` - 导出业务商品配置
- `business:order-relation:query` - 查询业务订单关联
- `business:order-relation:update` - 更新业务订单关联

### 用户 App权限

- **需要用户登录的接口**：不加任何注解（默认需要认证）
- **无需认证的接口**：使用 `@PermitAll` 注解
- **获取当前登录用户**：使用 `SecurityFrameworkUtils.getLoginUserId()` 静态方法

## 扩展说明

### 商品价格集成

目前价格信息部分使用示例数据，实际使用时需要：

1. 调用商品模块API获取实时价格信息
2. 根据用户会员等级计算会员价
3. 集成会员折扣系统

### 业务实体创建

在 `BusinessOrderRelationServiceImpl.createBusinessEntity()` 方法中，需要根据不同业务类型调用对应的业务服务：

- 数据存证：调用存证服务创建存证记录
- 软著登记：调用软著服务创建登记记录
- 实名认证：调用认证服务创建认证记录
- 版权登记：调用版权服务创建登记记录
- 区块链存证：调用区块链服务创建存证记录

## 部署说明

### 1. 依赖配置
确保 `yudao-module-business/pom.xml` 中已添加商品模块依赖：
```xml
<!-- 商品模块依赖 -->
<dependency>
    <groupId>cn.iocoder.boot</groupId>
    <artifactId>yudao-module-product</artifactId>
    <version>${revision}</version>
</dependency>
```

### 2. 编译验证
```bash
# 编译business模块
mvn clean compile -pl yudao-module-business

# 如果编译失败，先编译product模块
mvn clean install -pl yudao-module-mall/yudao-module-product
```

### 3. 数据库部署
执行数据库脚本：`sql/mysql/business-product-config-2025-01-13.sql`

### 4. 权限配置
配置相关权限（详见权限配置章节）

### 5. 应用重启
重启应用服务

### 6. 功能验证
验证接口功能（使用提供的API测试文件）

## 注意事项

1. 确保商品SPU在创建配置前已存在
2. 业务类型必须是预定义的枚举值
3. 默认配置的唯一性由应用层控制
4. 删除配置前需要检查是否有关联的订单
5. 用户 App接口需要用户登录才能访问
