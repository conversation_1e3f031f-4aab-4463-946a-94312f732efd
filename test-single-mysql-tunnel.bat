@echo off
echo Testing Single SSH Tunnel
echo ==========================

set JUMP_HOST=**************
set JUMP_USER=tunneluser
set JUMP_PORT=2222

REM Mysql production environment configuration
set LOCAL_PORT=3307
set REMOTE_HOST=***********
set REMOTE_PORT=4000

set PPK_KEY_PATH=..\2panel.jtstar.net.ppk

echo Testing database tunnel with key file...
echo Command: plink -ssh -L %LOCAL_PORT%:%REMOTE_HOST%:%REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
echo.
echo This should connect successfully and then wait for Enter key.
echo After you see "Access granted. Press Return to begin session.", press Enter.
echo.
pause

echo Starting tunnel...
plink -ssh -L %LOCAL_PORT%:%REMOTE_HOST%:%REMOTE_PORT% -P %JUMP_PORT% -i "%PPK_KEY_PATH%" %JUMP_USER%@%JUMP_HOST% -N
