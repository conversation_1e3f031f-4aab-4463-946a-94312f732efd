# 🚀 框架升级指南

本文档详细说明如何将基于芋道框架的项目升级到最新版本，包括从JDK 8升级到JDK 21，从Spring Boot 2.7升级到Spring Boot 3.4.5的完整流程。

## 📋 升级前准备

### 环境要求

**升级到JDK 21 + Spring Boot 3.4.5版本需要：**
- JDK 21（推荐使用LTS版本）
- Maven 3.6.3+
- MySQL 5.7+ / 8.0+
- Redis 5.0+

### 兼容性检查

升级前请确认以下组件的兼容性：
- 第三方依赖库是否支持Spring Boot 3.x
- 自定义代码是否使用了过时的API
- 数据库驱动版本是否兼容

## 🔄 升级流程

### 第一步：项目备份

```bash
# 1. 创建备份分支
git checkout -b backup-$(date +%Y%m%d)
git push origin backup-$(date +%Y%m%d)

# 2. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# 3. 备份配置文件
cp -r src/main/resources/application*.yml backup/
```

### 第二步：添加上游仓库

```bash
# 添加官方仓库为上游源
git remote add upstream https://gitee.com/zhijiantianya/ruoyi-vue-pro.git

# 验证远程仓库配置
git remote -v
# 输出应该包含：
# origin    <你的仓库地址> (fetch)
# origin    <你的仓库地址> (push)
# upstream  https://gitee.com/zhijiantianya/ruoyi-vue-pro.git (fetch)
# upstream  https://gitee.com/zhijiantianya/ruoyi-vue-pro.git (push)
```

### 第三步：获取最新代码

```bash
# 获取上游仓库的所有分支和标签
git fetch upstream

# 查看可用的分支
git branch -r | grep upstream
```

### 第四步：选择升级路径

#### 路径A：升级到JDK 8 + Spring Boot 2.7（稳定版）

```bash
# 切换到主分支
git checkout master

# 合并上游master分支
git merge upstream/master --no-ff -m "upgrade: 升级框架到Spring Boot 2.7最新版本"
```

#### 路径B：升级到JDK 17/21 + Spring Boot 3.4.5（推荐）

```bash
# 切换到主分支
git checkout master

# 合并上游master-jdk17分支
git merge upstream/master-jdk17 --no-ff -m "upgrade: 升级框架到Spring Boot 3.4.5 + JDK 21"
```

### 第五步：解决合并冲突

升级过程中可能遇到的冲突类型：

#### 1. POM文件冲突
```bash
# 查看冲突文件
git status

# 编辑pom.xml，保留业务模块配置
# 重点关注：
# - 自定义依赖版本
# - 业务模块声明
# - 自定义插件配置
```

#### 2. 配置文件冲突
```bash
# 合并application.yml配置
# 保留：
# - 数据库连接配置
# - Redis配置
# - 自定义业务配置
# 更新：
# - 框架相关配置
# - 新增的配置项
```

#### 3. 业务代码冲突
```bash
# 确保以下业务模块完整保留：
# - yudao-module-member（会员模块）
# - yudao-module-bisness（业务模块）
# - yudao-module-external（外部接口模块）
# - yudao-module-ai（AI聊天模块）
```

#### 4. 完成冲突解决
```bash
# 标记所有冲突已解决
git add .

# 完成合并提交
git commit -m "resolve: 解决升级过程中的代码冲突"
```

### 第六步：代码适配

#### Jakarta EE迁移（Spring Boot 3.x必需）

如果升级到Spring Boot 3.x，需要进行Jakarta EE迁移：

```bash
# 批量替换javax包名为jakarta
find src -name "*.java" -exec sed -i 's/javax\.servlet/jakarta.servlet/g' {} \;
find src -name "*.java" -exec sed -i 's/javax\.persistence/jakarta.persistence/g' {} \;
find src -name "*.java" -exec sed -i 's/javax\.validation/jakarta.validation/g' {} \;
```

#### 时间精度问题修复

JDK 21中LocalDateTime精度更高，可能导致测试失败：

```java
// 在测试工具类中添加时间比较方法
public static void assertTimeEquals(LocalDateTime expected, LocalDateTime actual) {
    assertEquals(expected.truncatedTo(ChronoUnit.MILLIS), 
                actual.truncatedTo(ChronoUnit.MILLIS));
}
```

### 第七步：依赖更新

```bash
# 清理Maven缓存
mvn clean

# 强制更新依赖
mvn dependency:purge-local-repository

# 重新下载依赖
mvn dependency:resolve

# 检查依赖冲突
mvn dependency:tree | grep -i conflict
```

### 第八步：数据库升级

```bash
# 1. 检查SQL升级脚本
ls sql/mysql/

# 2. 执行增量SQL脚本（按版本顺序）
mysql -u username -p database_name < sql/mysql/upgrade_v3.0.0.sql

# 3. 验证数据库结构
mysql -u username -p -e "SHOW TABLES;" database_name
```

### 第九步：编译测试

```bash
# 编译项目
mvn clean compile

# 运行单元测试
mvn test

# 如果测试失败，查看详细错误
mvn test -X
```

### 第十步：功能验证

```bash
# 启动项目
mvn spring-boot:run

# 验证核心功能：
# 1. 用户登录
# 2. 菜单权限
# 3. 业务模块功能
# 4. AI聊天功能
# 5. 会员注册功能
```

### 第十一步：推送更新

```bash
# 推送到远程仓库
git push origin master

# 创建升级标签
git tag -a v3.0.0-upgrade -m "升级到Spring Boot 3.4.5 + JDK 21"
git push origin v3.0.0-upgrade
```

## ⚠️ 升级注意事项

### 1. 版本兼容性

**JDK版本升级：**
- JDK 8 → JDK 17：注意模块系统变化
- JDK 8 → JDK 21：注意时间API精度变化
- 检查第三方库的JDK兼容性

**Spring Boot版本升级：**
- 2.x → 3.x：必须进行Jakarta EE迁移
- 配置文件格式可能有变化
- 某些过时的API被移除

### 2. 业务代码保护

**关键业务模块：**
- 会员注册和管理功能
- AI聊天对话功能
- 外部接口集成
- 自定义业务逻辑

**数据安全：**
- 升级前完整备份数据库
- 测试环境先行验证
- 保留回滚方案

### 3. 配置文件更新

**必须检查的配置：**
- 数据库连接配置
- Redis连接配置
- 第三方服务配置（支付、短信等）
- 文件存储配置
- 安全相关配置

### 4. 性能优化

**JDK 21性能优化：**
- 启用新的垃圾收集器
- 优化JVM参数
- 利用新的语言特性

**Spring Boot 3.x优化：**
- 启用原生镜像支持
- 优化启动时间
- 减少内存占用

## 🐛 常见问题解决

### Q1: 合并时出现大量冲突怎么办？

**解决方案：**
```bash
# 1. 取消当前合并
git merge --abort

# 2. 采用选择性合并策略
git checkout upstream/master-jdk17 -- yudao-framework/
git checkout upstream/master-jdk17 -- yudao-dependencies/
git checkout upstream/master-jdk17 -- pom.xml

# 3. 手动合并业务模块
# 保留本地的业务模块，只更新框架部分
```

### Q2: 升级后项目无法启动？

**排查步骤：**
```bash
# 1. 检查JDK版本
java -version

# 2. 检查Maven配置
mvn -version

# 3. 查看启动日志
mvn spring-boot:run -X

# 4. 检查依赖冲突
mvn dependency:tree | grep -i conflict
```

### Q3: 测试用例失败怎么办？

**解决方案：**
```bash
# 1. 跳过测试先启动项目
mvn spring-boot:run -DskipTests

# 2. 单独运行失败的测试
mvn test -Dtest=FailedTestClass

# 3. 修复时间精度问题
# 参考本文档第六步的时间精度修复方案
```

### Q4: 如何保持与上游同步？

**最佳实践：**
```bash
# 1. 定期获取上游更新
git fetch upstream

# 2. 查看更新内容
git log HEAD..upstream/master-jdk17 --oneline

# 3. 选择性合并功能
git cherry-pick <commit-hash>

# 4. 创建功能分支测试
git checkout -b feature-update
git merge upstream/master-jdk17
```

## 📞 技术支持

如果在升级过程中遇到问题，可以通过以下方式获取帮助：

1. **查看官方文档**：https://doc.iocoder.cn/
2. **提交Issue**：https://gitee.com/zhijiantianya/ruoyi-vue-pro/issues
3. **加入交流群**：参考项目README中的联系方式

## 📝 升级检查清单

升级完成后，请按照以下清单验证：

- [ ] 项目能够正常编译
- [ ] 单元测试全部通过
- [ ] 项目能够正常启动
- [ ] 用户登录功能正常
- [ ] 菜单权限功能正常
- [ ] 会员注册功能正常
- [ ] AI聊天功能正常
- [ ] 外部接口功能正常
- [ ] 数据库连接正常
- [ ] Redis连接正常
- [ ] 文件上传功能正常
- [ ] 第三方服务集成正常

升级成功后，建议在生产环境部署前进行充分的测试验证。
