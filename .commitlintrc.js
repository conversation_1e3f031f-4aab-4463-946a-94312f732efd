module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    // 类型枚举，git提交type必须是以下类型
    'type-enum': [
      2,
      'always',
      [
        'feat',     // 新增功能
        'fix',      // 修复缺陷
        'docs',     // 文档变更
        'style',    // 代码格式（不影响功能，例如空格、分号等格式修正）
        'refactor', // 代码重构（不包括 bug 修复、功能新增）
        'perf',     // 性能优化
        'test',     // 添加疏漏测试或已有测试改动
        'build',    // 构建流程、外部依赖变更（如升级 npm 包、修改 webpack 配置等）
        'ci',       // 修改 CI 配置、脚本
        'chore',    // 对构建过程或辅助工具和库的更改（不影响源文件、测试用例）
        'revert',   // 回滚 commit
        'wip',      // 开发中
        'workflow', // 工作流改进
        'types',    // 类型修改
        'release',  // 版本发布
        'update',   // 更新文件
        'add'       // 添加文件或功能
      ]
    ],
    // subject 大小写不做校验
    'subject-case': [0],
    // type 必须小写
    'type-case': [2, 'always', 'lower-case'],
    // type 不能为空
    'type-empty': [2, 'never'],
    // subject 不能为空
    'subject-empty': [2, 'never'],
    // subject 以.结尾
    'subject-full-stop': [0, 'never'],
    // header 最大长度
    'header-max-length': [2, 'always', 100]
  }
};
